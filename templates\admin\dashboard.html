                            {% extends 'base.html' %}

{% block title %}Admin Dashboard - {{ block.super }}{% endblock %}



{% block sidebar %}
    {% include 'admin/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<!-- Quick Stats Cards with Glassmorphism -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Students -->
    <div class="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-sm font-medium text-gray-600 mb-1">Total Students</h3>
                <p class="text-3xl font-bold text-primary-600">{{ total_students|default:0 }}</p>
                <p class="text-xs text-gray-500 mt-1">Active enrollments</p>
            </div>
            <div class="p-3 bg-primary-100 rounded-full">
                <svg class="w-6 h-6 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H2z"/>
                </svg>
            </div>
        </div>
    </div>

    <!-- Total Courses -->
    <div class="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-sm font-medium text-gray-600 mb-1">Total Courses</h3>
                <p class="text-3xl font-bold text-green-600">{{ total_courses|default:0 }}</p>
                <p class="text-xs text-gray-500 mt-1">Available courses</p>
            </div>
            <div class="p-3 bg-green-100 rounded-full">
                <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"/>
                </svg>
            </div>
        </div>
    </div>

    <!-- Departments -->
    <div class="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-sm font-medium text-gray-600 mb-1">Departments</h3>
                <p class="text-3xl font-bold text-purple-600">{{ total_departments|default:0 }}</p>
                <p class="text-xs text-gray-500 mt-1">Academic departments</p>
            </div>
            <div class="p-3 bg-purple-100 rounded-full">
                <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm6 0a2 2 0 104 0 2 2 0 00-4 0z" clip-rule="evenodd"/>
                </svg>
            </div>
        </div>
    </div>

    <!-- Average GPA -->
    <div class="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 hover:scale-105">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-sm font-medium text-gray-600 mb-1">Average GPA</h3>
                <p class="text-3xl font-bold text-orange-600">{{ avg_gpa|default:"0.00" }}</p>
                <p class="text-xs text-gray-500 mt-1">Overall performance</p>
            </div>
            <div class="p-3 bg-orange-100 rounded-full">
                <svg class="w-6 h-6 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
            </div>
        </div>
    </div>
</div>

<!-- Dashboard Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Recent Activity -->
    <div class="lg:col-span-2">
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-semibold text-gray-800">Recent Activity</h2>
                <button class="text-primary-600 hover:text-primary-700 text-sm font-medium">View All</button>
            </div>
            <div hx-get="{% url 'api_recent_activity' %}" hx-trigger="load" hx-swap="innerHTML">
                <div class="htmx-indicator flex items-center justify-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                    <span class="ml-2 text-gray-600">Loading recent activity...</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions & Stats -->
    <div class="space-y-6">
        <!-- Quick Actions -->
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
            <div class="space-y-3">
                <a href="{% url 'management_advising' %}" class="flex items-center p-3 rounded-lg bg-blue-50 hover:bg-blue-100 transition-colors group">
                    <svg class="w-5 h-5 text-blue-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-gray-700 group-hover:text-blue-700">Schedule Session</p>
                        <p class="text-xs text-gray-500">Book new advising appointment</p>
                    </div>
                </a>
                <a href="{% url 'management_students' %}" class="flex items-center p-3 rounded-lg bg-green-50 hover:bg-green-100 transition-colors group">
                    <svg class="w-5 h-5 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-gray-700 group-hover:text-green-700">Review Progress</p>
                        <p class="text-xs text-gray-500">Check student academic progress</p>
                    </div>
                </a>
                <a href="{% url 'management_reports' %}" class="flex items-center p-3 rounded-lg bg-purple-50 hover:bg-purple-100 transition-colors group">
                    <svg class="w-5 h-5 text-purple-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zM6 7a1 1 0 011-1h4a1 1 0 110 2H7a1 1 0 01-1-1z M6 9a1 1 0 011-1h4a1 1 0 110 2H7a1 1 0 01-1-1z M6 11a1 1 0 011-1h4a1 1 0 110 2H7a1 1 0 01-1-1z"/>
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-gray-700 group-hover:text-purple-700">Generate Report</p>
                        <p class="text-xs text-gray-500">Create advising summary report</p>
                    </div>
                </a>
            </div>
        </div>

        <!-- Students by Year -->
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Students by Year</h3>
            <div class="space-y-3">
                {% for year_label, count in students_by_year.items %}
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">{{ year_label }}</span>
                    <span class="text-sm font-semibold text-gray-800">{{ count }}</span>
                </div>
                {% empty %}
                <p class="text-sm text-gray-500">No student data available</p>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
