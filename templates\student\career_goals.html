{% extends 'student/base.html' %}

{% block title %}Career Goals - Course Recommendation System{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">

    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Career Goals</h1>
        <p class="mt-2 text-gray-600">Define your career objectives to receive targeted course recommendations that align with your professional aspirations.</p>
    </div>

    <!-- Career Goals Form -->
    <div class="bg-white rounded-lg shadow mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Career Goals & Preferences</h2>
        </div>

        <form method="post" class="p-6">
            {% csrf_token %}

            {% if form.non_field_errors %}
                <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">
                                Please correct the following errors:
                            </h3>
                            <div class="mt-2 text-sm text-red-700">
                                {{ form.non_field_errors }}
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <div class="space-y-6">
                <div>
                    <label for="{{ form.career_goals.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Career Goals & Aspirations
                    </label>
                    {{ form.career_goals }}
                    {% if form.career_goals.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.career_goals.errors.0 }}</p>
                    {% endif %}
                    <p class="mt-2 text-sm text-gray-500">Describe your career goals, aspirations, and what you hope to achieve after graduation.</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="{{ form.preferred_difficulty.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Preferred Course Difficulty
                        </label>
                        {{ form.preferred_difficulty }}
                        {% if form.preferred_difficulty.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.preferred_difficulty.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.expected_graduation_year.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Expected Graduation Year
                        </label>
                        {{ form.expected_graduation_year }}
                        {% if form.expected_graduation_year.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.expected_graduation_year.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>

                <div class="flex justify-end">
                    <button type="submit"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Save Career Goals
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Current Goals Display -->
    {% if student_profile.career_goals %}
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Current Career Goals</h2>
        </div>

        <div class="p-6">
            <div class="prose max-w-none">
                <p class="text-gray-700">{{ student_profile.career_goals|linebreaks }}</p>
            </div>

            <div class="mt-4 flex items-center space-x-4">
                {% if student_profile.preferred_difficulty %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Difficulty: {{ student_profile.get_preferred_difficulty_display }}
                </span>
                {% endif %}

                {% if student_profile.expected_graduation_year %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Graduation: {{ student_profile.expected_graduation_year }}
                </span>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}
</div>

{% endblock %}
