{% extends 'base.html' %}

{% block title %}Admission Test Details - {{ block.super }}{% endblock %}

{% block sidebar %}
    {% include 'admin/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<div class="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-white/20">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h2 class="text-2xl font-semibold text-gray-800">Admission Test Question Details</h2>
            <p class="text-gray-600 mt-1">View question details and performance statistics</p>
        </div>
        <div class="flex space-x-2">
            <a href="{% url 'management_admission_tests' %}" 
               class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition-transform transform hover:scale-105 inline-flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to List
            </a>
            <a href="{% url 'admin_admission_test_edit' test.id %}" 
               class="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition-transform transform hover:scale-105 inline-flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit Question
            </a>
        </div>
    </div>

    <!-- Question Information -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <!-- Question Details -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg border border-gray-200 p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Question Information</h3>
                
                <!-- Status and Metadata -->
                <div class="flex items-center space-x-3 mb-4">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                        {% if test.subject_area == 'math' %}bg-blue-100 text-blue-800
                        {% elif test.subject_area == 'science' %}bg-green-100 text-green-800
                        {% elif test.subject_area == 'english' %}bg-purple-100 text-purple-800
                        {% elif test.subject_area == 'history' %}bg-amber-100 text-amber-800
                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                        {{ test.get_subject_area_display }}
                    </span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        {% if test.difficulty_level == 'easy' %}bg-green-100 text-green-800
                        {% elif test.difficulty_level == 'medium' %}bg-yellow-100 text-yellow-800
                        {% else %}bg-red-100 text-red-800{% endif %}">
                        {{ test.get_difficulty_level_display }}
                    </span>
                    {% if test.is_active %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        Active
                    </span>
                    {% else %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clip-rule="evenodd"></path>
                        </svg>
                        Inactive
                    </span>
                    {% endif %}
                </div>
                
                <!-- Question Text -->
                <div class="mb-6">
                    <h4 class="text-sm font-medium text-gray-700 mb-2">Question:</h4>
                    <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <p class="text-gray-900">{{ test.question_text }}</p>
                    </div>
                </div>
                
                <!-- Answer Options -->
                <div class="mb-6">
                    <h4 class="text-sm font-medium text-gray-700 mb-3">Answer Options:</h4>
                    <div class="space-y-2">
                        {% for option in test.answer_options %}
                        <div class="flex items-center p-3 rounded-lg border 
                            {% if option == test.correct_answer %}border-green-300 bg-green-50{% else %}border-gray-200 bg-white{% endif %}">
                            <span class="flex-shrink-0 w-6 h-6 bg-gray-200 text-gray-700 rounded-full flex items-center justify-center text-sm font-medium mr-3">
                                {{ forloop.counter }}
                            </span>
                            <span class="text-gray-900">{{ option }}</span>
                            {% if option == test.correct_answer %}
                            <svg class="w-5 h-5 text-green-600 ml-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                </div>
                
                <!-- Explanation -->
                {% if test.explanation %}
                <div>
                    <h4 class="text-sm font-medium text-gray-700 mb-2">Explanation:</h4>
                    <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
                        <p class="text-blue-900">{{ test.explanation }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Statistics -->
        <div class="space-y-6">
            <!-- Performance Stats -->
            <div class="bg-white rounded-lg border border-gray-200 p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Performance Statistics</h3>
                
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Total Attempts:</span>
                        <span class="text-lg font-semibold text-gray-900">{{ total_attempts }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Correct Answers:</span>
                        <span class="text-lg font-semibold text-green-600">{{ correct_attempts }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Accuracy Rate:</span>
                        <span class="text-lg font-semibold 
                            {% if accuracy_rate >= 80 %}text-green-600
                            {% elif accuracy_rate >= 60 %}text-yellow-600
                            {% else %}text-red-600{% endif %}">
                            {{ accuracy_rate }}%
                        </span>
                    </div>
                </div>
                
                <!-- Accuracy Visual -->
                <div class="mt-4">
                    <div class="flex justify-between text-sm text-gray-600 mb-1">
                        <span>Accuracy</span>
                        <span>{{ accuracy_rate }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="h-2 rounded-full 
                            {% if accuracy_rate >= 80 %}bg-green-500
                            {% elif accuracy_rate >= 60 %}bg-yellow-500
                            {% else %}bg-red-500{% endif %}" 
                             style="width: {{ accuracy_rate }}%"></div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="bg-white rounded-lg border border-gray-200 p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                
                <div class="space-y-3">
                    <form method="post" action="{% url 'admin_admission_test_toggle_status' test.id %}">
                        {% csrf_token %}
                        <button type="submit" 
                                class="w-full {% if test.is_active %}bg-red-600 hover:bg-red-700{% else %}bg-green-600 hover:bg-green-700{% endif %} text-white font-medium py-2 px-4 rounded-lg transition-colors">
                            {% if test.is_active %}Deactivate Question{% else %}Activate Question{% endif %}
                        </button>
                    </form>
                    
                    <a href="{% url 'admin_admission_test_delete' test.id %}" 
                       class="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-center block">
                        Delete Question
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Attempts -->
    {% if recent_attempts %}
    <div class="bg-white rounded-lg border border-gray-200">
        <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Student Attempts</h3>
        </div>
        
        <div class="divide-y divide-gray-200">
            {% for attempt in recent_attempts %}
            <div class="p-6 hover:bg-gray-50 transition-colors duration-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                                <span class="text-sm font-medium text-gray-700">
                                    {{ attempt.attempt.student.user.first_name|first|default:attempt.attempt.student.user.username|first|upper }}{{ attempt.attempt.student.user.last_name|first|upper }}
                                </span>
                            </div>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900">
                                {{ attempt.attempt.student.user.get_full_name|default:attempt.attempt.student.user.username }}
                            </p>
                            <p class="text-sm text-gray-500">
                                {{ attempt.attempt.date_taken|date:"M d, Y H:i" }}
                            </p>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <div class="text-right">
                            <p class="text-sm text-gray-600">Answer:</p>
                            <p class="text-sm font-medium">{{ attempt.student_answer }}</p>
                        </div>
                        <div class="flex-shrink-0">
                            {% if attempt.student_answer == test.correct_answer %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Correct
                            </span>
                            {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                                Incorrect
                            </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% else %}
    <div class="bg-white rounded-lg border border-gray-200 p-8 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No attempts yet</h3>
        <p class="mt-1 text-sm text-gray-500">This question hasn't been attempted by any students yet.</p>
    </div>
    {% endif %}
</div>
{% endblock %}
