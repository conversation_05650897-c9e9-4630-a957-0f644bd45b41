{% extends 'student/base.html' %}

{% block title %}Complete Your Profile - Course Recommendation System{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">

    <!-- Header -->
    <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Complete Your Profile</h1>
        <p class="text-gray-600">Complete all sections below to unlock personalized course recommendations</p>
        
        <!-- Overall Progress -->
        <div class="mt-6 max-w-md mx-auto">
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-700">Overall Progress</span>
                <span class="text-sm font-medium text-gray-900">{{ progress.completed_count }}/{{ progress.total_count }} Complete</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-3">
                <div class="bg-gradient-to-r from-primary-500 to-primary-600 h-3 rounded-full transition-all duration-500"
                     style="width: {{ progress.percentage }}%"></div>
            </div>
            <p class="text-xs text-gray-500 mt-1">{{ progress.percentage|floatformat:0 }}% Complete</p>
        </div>
    </div>

    <!-- Profile Completion Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        
        <!-- 1. Academic Records -->
        <div class="glassmorphism rounded-xl p-6 {% if progress.academic_records %}border-green-200{% else %}border-amber-200{% endif %}">
            <div class="flex items-start justify-between mb-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 mr-3">
                        {% if progress.academic_records %}
                        <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        {% else %}
                        <div class="w-10 h-10 bg-amber-500 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </div>
                        {% endif %}
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold {% if progress.academic_records %}text-green-800{% else %}text-amber-800{% endif %}">
                            Academic Records
                        </h3>
                        <p class="text-sm {% if progress.academic_records %}text-green-600{% else %}text-amber-600{% endif %}">
                            {% if progress.academic_records %}✓ Completed{% else %}Add your course history{% endif %}
                        </p>
                    </div>
                </div>
                {% if progress.academic_records %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Complete
                </span>
                {% else %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                    Required
                </span>
                {% endif %}
            </div>
            <p class="text-gray-600 text-sm mb-4">
                Add your previous courses and grades to help us understand your academic background.
            </p>
            <a href="{% url 'academic_records' %}" 
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 transition-all duration-200">
                {% if progress.academic_records %}View Records{% else %}Add Records{% endif %}
                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </a>
        </div>

        <!-- 2. Academic Interests -->
        <div class="glassmorphism rounded-xl p-6 {% if progress.interests %}border-green-200{% else %}border-amber-200{% endif %}">
            <div class="flex items-start justify-between mb-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 mr-3">
                        {% if progress.interests %}
                        <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        {% else %}
                        <div class="w-10 h-10 bg-amber-500 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </div>
                        {% endif %}
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold {% if progress.interests %}text-green-800{% else %}text-amber-800{% endif %}">
                            Academic Interests
                        </h3>
                        <p class="text-sm {% if progress.interests %}text-green-600{% else %}text-amber-600{% endif %}">
                            {% if progress.interests %}✓ Completed{% else %}Set your interests{% endif %}
                        </p>
                    </div>
                </div>
                {% if progress.interests %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Complete
                </span>
                {% else %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                    Required
                </span>
                {% endif %}
            </div>
            <p class="text-gray-600 text-sm mb-4">
                Tell us about your academic interests and areas you'd like to explore.
            </p>
            <a href="{% url 'student_interests' %}" 
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 transition-all duration-200">
                {% if progress.interests %}View Interests{% else %}Add Interests{% endif %}
                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </a>
        </div>

        <!-- 3. Career Goals -->
        <div class="glassmorphism rounded-xl p-6 {% if progress.career_goals %}border-green-200{% else %}border-amber-200{% endif %}">
            <div class="flex items-start justify-between mb-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 mr-3">
                        {% if progress.career_goals %}
                        <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        {% else %}
                        <div class="w-10 h-10 bg-amber-500 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </div>
                        {% endif %}
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold {% if progress.career_goals %}text-green-800{% else %}text-amber-800{% endif %}">
                            Career Goals
                        </h3>
                        <p class="text-sm {% if progress.career_goals %}text-green-600{% else %}text-amber-600{% endif %}">
                            {% if progress.career_goals %}✓ Completed{% else %}Define your goals{% endif %}
                        </p>
                    </div>
                </div>
                {% if progress.career_goals %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Complete
                </span>
                {% else %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                    Required
                </span>
                {% endif %}
            </div>
            <p class="text-gray-600 text-sm mb-4">
                Describe your career aspirations and what you hope to achieve after graduation.
            </p>
            <a href="{% url 'career_goals' %}" 
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 transition-all duration-200">
                {% if progress.career_goals %}View Goals{% else %}Set Goals{% endif %}
                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </a>
        </div>

        <!-- 4. Academic Major -->
        <div class="glassmorphism rounded-xl p-6 {% if progress.major %}border-green-200{% else %}border-red-200{% endif %} {% if not progress.major %}ring-2 ring-red-300{% endif %}">
            <div class="flex items-start justify-between mb-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 mr-3">
                        {% if progress.major %}
                        <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        {% else %}
                        <div class="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center animate-pulse">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        {% endif %}
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold {% if progress.major %}text-green-800{% else %}text-red-800{% endif %}">
                            Academic Major
                        </h3>
                        <p class="text-sm {% if progress.major %}text-green-600{% else %}text-red-600{% endif %}">
                            {% if progress.major %}✓ Selected: {{ student.major.name }}{% else %}⚠️ Select your major{% endif %}
                        </p>
                    </div>
                </div>
                {% if progress.major %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Complete
                </span>
                {% else %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 animate-pulse">
                    Critical
                </span>
                {% endif %}
            </div>
            <p class="text-gray-600 text-sm mb-4">
                {% if not progress.major %}
                <strong class="text-red-600">Important:</strong> Select your academic major to unlock course recommendations.
                {% else %}
                Your academic major helps us recommend relevant courses in your field of study.
                {% endif %}
            </p>
            <a href="{% url 'select_major' %}"
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white {% if progress.major %}bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700{% else %}bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 shadow-lg{% endif %} transition-all duration-200">
                {% if progress.major %}Change Major{% else %}Select Major{% endif %}
                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </a>
        </div>

        <!-- 5. Admission Test -->
        <div class="glassmorphism rounded-xl p-6 {% if progress.admission_test %}border-green-200{% else %}border-amber-200{% endif %}">
            <div class="flex items-start justify-between mb-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 mr-3">
                        {% if progress.admission_test %}
                        <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        {% else %}
                        <div class="w-10 h-10 bg-amber-500 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        {% endif %}
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold {% if progress.admission_test %}text-green-800{% else %}text-amber-800{% endif %}">
                            Admission Test
                        </h3>
                        <p class="text-sm {% if progress.admission_test %}text-green-600{% else %}text-amber-600{% endif %}">
                            {% if progress.admission_test %}✓ Completed{% else %}Take assessment{% endif %}
                        </p>
                    </div>
                </div>
                {% if progress.admission_test %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Complete
                </span>
                {% else %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                    Required
                </span>
                {% endif %}
            </div>
            <p class="text-gray-600 text-sm mb-4">
                Complete our assessment to help us understand your academic strengths and preferences.
            </p>
            <a href="{% if progress.admission_test %}{% url 'admission_test_results' %}{% else %}{% url 'admission_test_start' %}{% endif %}"
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 transition-all duration-200">
                {% if progress.admission_test %}View Results{% else %}Start Test{% endif %}
                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </a>
        </div>

        <!-- 6. Learning Survey -->
        <div class="glassmorphism rounded-xl p-6 {% if progress.survey %}border-green-200{% else %}border-amber-200{% endif %}">
            <div class="flex items-start justify-between mb-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 mr-3">
                        {% if progress.survey %}
                        <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        {% else %}
                        <div class="w-10 h-10 bg-amber-500 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        {% endif %}
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold {% if progress.survey %}text-green-800{% else %}text-amber-800{% endif %}">
                            Learning Survey
                        </h3>
                        <p class="text-sm {% if progress.survey %}text-green-600{% else %}text-amber-600{% endif %}">
                            {% if progress.survey %}✓ Completed{% else %}Share preferences{% endif %}
                        </p>
                    </div>
                </div>
                {% if progress.survey %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Complete
                </span>
                {% else %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                    Required
                </span>
                {% endif %}
            </div>
            <p class="text-gray-600 text-sm mb-4">
                Tell us about your learning style and preferences to get better course recommendations.
            </p>
            <a href="{% url 'student_survey' %}"
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 transition-all duration-200">
                {% if progress.survey %}View Survey{% else %}Take Survey{% endif %}
                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </a>
        </div>
    </div>

    <!-- Completion Status -->
    {% if progress.all_completed %}
    <div class="glassmorphism rounded-xl p-8 text-center border-green-200 bg-gradient-to-r from-green-50 to-emerald-50">
        <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
            </svg>
        </div>
        <h3 class="text-2xl font-bold text-green-800 mb-2">🎉 Profile Complete!</h3>
        <p class="text-green-600 mb-6">Congratulations! You've completed all required sections. You can now access personalized course recommendations.</p>
        <a href="{% url 'recommendations' %}"
           class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 transition-all duration-200 shadow-lg">
            View My Recommendations
            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
            </svg>
        </a>
    </div>
    {% else %}
    <div class="glassmorphism rounded-xl p-8 text-center border-amber-200 bg-gradient-to-r from-amber-50 to-orange-50">
        <div class="w-16 h-16 bg-amber-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
        </div>
        <h3 class="text-2xl font-bold text-amber-800 mb-2">Almost There!</h3>
        <p class="text-amber-600 mb-6">Complete the remaining {{ progress.total_count|add:"-"|add:progress.completed_count }} section{{ progress.total_count|add:"-"|add:progress.completed_count|pluralize }} above to unlock your personalized course recommendations.</p>
        <a href="{% url 'student_dashboard' %}"
           class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-gradient-to-r from-amber-500 to-orange-600 hover:from-amber-600 hover:to-orange-700 transition-all duration-200">
            Back to Dashboard
            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
        </a>
    </div>
    {% endif %}

</div>
{% endblock %}
