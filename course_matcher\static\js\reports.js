import * as THREE from 'three';

document.addEventListener('DOMContentLoaded', () => {
    const container = document.getElementById('course-popularity-chart');
    if (!container) return;

    // Scene setup
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, container.clientWidth / 300, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    renderer.setSize(container.clientWidth, 300);
    renderer.setClearColor(0x000000, 0);
    container.appendChild(renderer.domElement);

    // Placeholder data (in a real app, this would come from an API)
    const courses = [
        { name: 'CS101', popularity: 85 },
        { name: 'MATH203', popularity: 70 },
        { name: 'PHYS101', popularity: 95 },
        { name: 'ENGL101', popularity: 60 },
        { name: 'HIST202', popularity: 75 },
    ];

    // Create bars for the chart
    const barWidth = 1;
    const barSpacing = 1.5;
    const maxBarHeight = 5;

    courses.forEach((course, index) => {
        const height = (course.popularity / 100) * maxBarHeight;
        const geometry = new THREE.BoxGeometry(barWidth, height, barWidth);
        const material = new THREE.MeshStandardMaterial({ 
            color: new THREE.Color(`hsl(${(index / courses.length) * 360}, 70%, 60%)`)
        });
        const bar = new THREE.Mesh(geometry, material);
        
        bar.position.x = index * barSpacing - ((courses.length - 1) * barSpacing) / 2;
        bar.position.y = height / 2;
        scene.add(bar);
    });

    // Lighting
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    scene.add(ambientLight);
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(5, 10, 7);
    scene.add(directionalLight);

    camera.position.set(0, 4, 10);
    camera.lookAt(0, 2, 0);

    // Animation loop
    function animate() {
        requestAnimationFrame(animate);
        renderer.render(scene, camera);
    }

    animate();

    // Handle resizing
    window.addEventListener('resize', () => {
        if (container.clientWidth > 0) {
            camera.aspect = container.clientWidth / 300;
            camera.updateProjectionMatrix();
            renderer.setSize(container.clientWidth, 300);
        }
    });
});
