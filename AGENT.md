# CourseRec Django Project

## Commands
- **Run dev server**: `python manage.py runserver`
- **Run all tests**: `python manage.py test`
- **Run specific test**: `python manage.py test course_matcher.tests.TestClassName.test_method_name`
- **Make migrations**: `python manage.py makemigrations`
- **Apply migrations**: `python manage.py migrate`
- **Create superuser**: `python manage.py createsuperuser`

## Architecture
- Django 5.1.1 project with course recommendation functionality
- Main project: `CourseRec/` (settings, URLs, WSGI/ASGI)
- Core app: `course_matcher/` (main business logic)
- Database: SQLite (`db.sqlite3`)
- Static files: `static/` directory
- Templates: `templates/` directory
- Media: `media/` directory

## Code Style
- Follow Django conventions and PEP 8
- Models in `course_matcher/models.py`
- Views in `course_matcher/views.py` using function-based views
- Templates rendered with `render(request, 'template.html')`
- Import Django modules first, then local modules
- Use Django's built-in authentication and admin
