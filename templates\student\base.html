{% extends 'base.html' %}

{% block sidebar %}

    <div class="h-full flex flex-col">
        <!-- Mobile close button -->
        <div class="sidebar-close-btn lg:hidden">
            <button @click="sidebarOpen = false" class="text-gray-500 hover:text-gray-700">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <!-- Header/App Title -->
        <div class="px-6 border-b border-gray-200 flex items-center" style="height:72px;min-height:72px;">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"></path>
                    </svg>
                </div>
                <div>
                    <h2 class="text-lg font-bold text-gray-900">Student Portal</h2>
                    <p class="text-xs text-gray-500">Course Recommendations</p>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="flex-1 px-4 py-6 space-y-2">
                        <a href="{% url 'student_dashboard' %}"
               class="sidebar-nav-item group flex items-center px-4 py-3 text-sm font-medium text-gray-700 rounded-xl transition-all duration-200 hover:bg-primary-50 hover:text-primary-700"
               up-follow>
                <svg class="sidebar-icon flex-shrink-0 w-5 h-5 mr-3 transition-colors duration-200 text-gray-400 group-hover:text-primary-600"
                     fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"></path>
                    <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"></path>
                </svg>
                <span>Dashboard</span>
            </a>
            <a href="{% url 'academic_records' %}"
               class="sidebar-nav-item group flex items-center px-4 py-3 text-sm font-medium text-gray-700 rounded-xl transition-all duration-200 hover:bg-primary-50 hover:text-primary-700"
               up-follow>
                <svg class="sidebar-icon flex-shrink-0 w-5 h-5 mr-3 transition-colors duration-200 text-gray-400 group-hover:text-primary-600"
                     fill="currentColor" viewBox="0 0 20 20">
                    <circle cx="10" cy="10" r="8" stroke="currentColor" stroke-width="2" fill="none" />
                    <circle cx="10" cy="10" r="3" fill="currentColor" />
                </svg>
                <span>Academic Records</span>
            </a>
            <a href="{% url 'student_interests' %}"
               class="sidebar-nav-item group flex items-center px-4 py-3 text-sm font-medium text-gray-700 rounded-xl transition-all duration-200 hover:bg-primary-50 hover:text-primary-700"
               up-follow>
                <svg class="sidebar-icon flex-shrink-0 w-5 h-5 mr-3 transition-colors duration-200 text-gray-400 group-hover:text-primary-600"
                     fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                </svg>
                <span>Interests</span>
            </a>
            <a href="{% url 'career_goals' %}"
               class="sidebar-nav-item group flex items-center px-4 py-3 text-sm font-medium text-gray-700 rounded-xl transition-all duration-200 hover:bg-primary-50 hover:text-primary-700"
               up-follow>
                <svg class="sidebar-icon flex-shrink-0 w-5 h-5 mr-3 transition-colors duration-200 text-gray-400 group-hover:text-primary-600"
                     fill="currentColor" viewBox="0 0 20 20">
                    <rect x="4" y="4" width="12" height="12" rx="3" fill="currentColor" />
                </svg>
                <span>Career Goals</span>
            </a>

            <!-- Assessment Section -->
            <div class="pt-4 pb-2">
                <h3 class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider">Assessment</h3>
            </div>

            <a href="{% url 'admission_test_start' %}"
               class="sidebar-nav-item group flex items-center px-4 py-3 text-sm font-medium text-gray-700 rounded-xl transition-all duration-200 hover:bg-primary-50 hover:text-primary-700"
               up-follow>
                <svg class="sidebar-icon flex-shrink-0 w-5 h-5 mr-3 transition-colors duration-200 text-gray-400 group-hover:text-primary-600"
                     fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>Admission Test</span>
            </a>

            <a href="{% url 'student_survey' %}"
               class="sidebar-nav-item group flex items-center px-4 py-3 text-sm font-medium text-gray-700 rounded-xl transition-all duration-200 hover:bg-primary-50 hover:text-primary-700"
               up-follow>
                <svg class="sidebar-icon flex-shrink-0 w-5 h-5 mr-3 transition-colors duration-200 text-gray-400 group-hover:text-primary-600"
                     fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                </svg>
                <span>Learning Survey</span>
            </a>

            <!-- Recommendations Section -->
            <div class="pt-4 pb-2">
                <h3 class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider">Recommendations</h3>
            </div>

            <a href="{% url 'recommendations' %}"
               class="sidebar-nav-item group flex items-center px-4 py-3 text-sm font-medium text-gray-700 rounded-xl transition-all duration-200 hover:bg-primary-50 hover:text-primary-700"
               up-follow>
                <svg class="sidebar-icon flex-shrink-0 w-5 h-5 mr-3 transition-colors duration-200 text-gray-400 group-hover:text-primary-600"
                     fill="currentColor" viewBox="0 0 20 20">
                    <polygon points="10,2 12,8 18,8 13,12 15,18 10,14 5,18 7,12 2,8 8,8" fill="currentColor" />
                </svg>
                <span>Recommendations</span>
            </a>
        </nav>

        <!-- Footer -->
        <div class="sidebar-footer">
            <div class="sidebar-user">
                <div class="sidebar-avatar">
                    {{ user.get_full_name|default:user.username|slice:":1"|upper }}
                </div>
                <div class="sidebar-user-info">
                    <p>{{ user.get_full_name|default:user.username }}</p>
                    <span>Student</span>
                </div>
                <form method="post" action="{% url 'logout' %}" class="ml-auto" up-target="_top">
                    {% csrf_token %}
                    <button type="submit" class="bg-transparent border-0 p-0 text-gray-400 hover:text-red-500 transition-colors duration-200" title="Logout">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                        </svg>
                    </button>
                </form>
            </div>
        </div>
    </div>

{% endblock %}

{% block main_classes %}flex-1 bg-gray-50{% endblock %}
