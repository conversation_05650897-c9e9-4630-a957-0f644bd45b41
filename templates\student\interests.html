{% extends 'student/base.html' %}

{% block title %}Interests - Course Recommendation System{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
    
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Academic Interests</h1>
        <p class="mt-2 text-gray-600">Manage your academic and personal interests to get better course recommendations.</p>
    </div>

    <!-- Current Interests Section -->
    <div class="bg-white rounded-lg shadow mb-8">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-xl font-semibold text-gray-900">Current Interests</h2>
            <span class="text-sm text-gray-500">{{ current_interests|length }} interest{{ current_interests|length|pluralize }}</span>
        </div>

        <div class="p-6">
            <div id="interests-container" class="flex flex-wrap gap-3">
                {% for interest in current_interests %}
                <div class="inline-flex items-center px-3 py-2 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-full text-sm font-medium text-blue-800 hover:from-blue-100 hover:to-indigo-100 transition-all duration-200">
                    <span>{{ interest }}</span>
                    <button hx-delete="{% url 'delete_interest' forloop.counter0 %}"
                            hx-confirm="Are you sure you want to remove '{{ interest }}'?"
                            hx-target="closest div"
                            hx-swap="outerHTML"
                            class="ml-2 text-blue-600 hover:text-blue-800 transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                {% empty %}
                <div class="text-center py-12 w-full">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No interests added yet</h3>
                    <p class="mt-1 text-sm text-gray-500">Start by adding your first interest to get personalized recommendations.</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Add Interest Form -->
    <div class="bg-white rounded-lg shadow mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Add New Interest</h2>
        </div>
        
        <form method="post" class="p-6">
            {% csrf_token %}

            {% if form.non_field_errors %}
                <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">
                                Please correct the following errors:
                            </h3>
                            <div class="mt-2 text-sm text-red-700">
                                {{ form.non_field_errors }}
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <div class="space-y-6">
                <div>
                    <label for="{{ form.new_interest.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Interest Name
                    </label>
                    {{ form.new_interest }}
                    {% if form.new_interest.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.new_interest.errors.0 }}</p>
                    {% endif %}
                </div>

                {{ form.interests }}

                <div class="flex justify-end">
                    <button type="submit"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add Interest
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Interest Suggestions -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Suggested Interests</h2>
            <p class="text-sm text-gray-600 mt-1">Popular interests based on your major and academic history</p>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {% for suggestion in suggested_interests %}
                <button hx-post="{% url 'add_suggested_interest' %}"
                        hx-vals='{"interest": "{{ suggestion }}"}'
                        hx-target="#interests-container"
                        hx-swap="beforeend"
                        hx-on::after-request="this.style.display = 'none'"
                        class="flex items-center px-3 py-2 bg-gray-100 hover:bg-primary-100 rounded-lg text-sm font-medium text-gray-700 hover:text-primary-700 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    {{ suggestion }}
                </button>
                {% empty %}
                <div class="col-span-full text-center py-8">
                    <p class="text-gray-500 text-sm">No suggestions available. Add your first interest to get personalized suggestions.</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<script>
// Simple JavaScript for interests management
document.addEventListener('DOMContentLoaded', function() {
    console.log('Interests page loaded');
});
</script>
{% endblock %}

{% block extra_css %}
<style>
    .htmx-request .htmx-indicator {
        opacity: 1;
    }
    
    .htmx-indicator {
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    [x-cloak] {
        display: none !important;
    }
</style>
{% endblock %}
