{% extends 'base.html' %}

{% block title %}{{ student.user.get_full_name }} - Student Details - {{ block.super }}{% endblock %}

{% block sidebar %}
    {% include 'admin/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto space-y-6">
    <!-- Header -->
    <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center">
                    <span class="text-2xl font-bold text-primary-600">
                        {{ student.user.first_name|first }}{{ student.user.last_name|first }}
                    </span>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ student.user.get_full_name }}</h1>
                    <p class="text-gray-600">{{ student.student_id }} • {{ student.get_year_display|default:"No year specified" }}</p>
                    {% if student.major %}
                    <p class="text-sm text-gray-500">{{ student.major.name }}</p>
                    {% endif %}
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'admin_student_edit' student.id %}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors inline-flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
                    </svg>
                    Edit Student
                </a>
                <a href="{% url 'management_students' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors inline-flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"/>
                    </svg>
                    Back to Students
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Personal Information -->
        <div class="lg:col-span-1">
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Personal Information</h2>
                <div class="space-y-4">
                    <div>
                        <label class="text-sm font-medium text-gray-500">Email</label>
                        <p class="text-gray-900">{{ student.user.email }}</p>
                    </div>
                    {% if student.phone_number %}
                    <div>
                        <label class="text-sm font-medium text-gray-500">Phone</label>
                        <p class="text-gray-900">{{ student.phone_number }}</p>
                    </div>
                    {% endif %}
                    {% if student.date_of_birth %}
                    <div>
                        <label class="text-sm font-medium text-gray-500">Date of Birth</label>
                        <p class="text-gray-900">{{ student.date_of_birth|date:"F j, Y" }}</p>
                    </div>
                    {% endif %}
                    {% if student.address %}
                    <div>
                        <label class="text-sm font-medium text-gray-500">Address</label>
                        <p class="text-gray-900">{{ student.address }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Academic Summary -->
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 mt-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Academic Summary</h2>
                <div class="space-y-4">
                    <div>
                        <label class="text-sm font-medium text-gray-500">Current GPA</label>
                        <p class="text-2xl font-bold text-primary-600">{{ student.gpa|floatformat:2|default:"N/A" }}</p>
                    </div>
                    {% if student.expected_graduation_year %}
                    <div>
                        <label class="text-sm font-medium text-gray-500">Expected Graduation</label>
                        <p class="text-gray-900">{{ student.expected_graduation_year }}</p>
                    </div>
                    {% endif %}
                    {% if student.preferred_difficulty %}
                    <div>
                        <label class="text-sm font-medium text-gray-500">Preferred Difficulty</label>
                        <p class="text-gray-900">{{ student.get_preferred_difficulty_display }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Academic Records and Career Goals -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Career Goals -->
            {% if student.career_goals %}
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Career Goals</h2>
                <p class="text-gray-700 leading-relaxed">{{ student.career_goals }}</p>
            </div>
            {% endif %}

            <!-- Academic Records -->
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-gray-800">Academic Records</h2>
                    <span class="text-sm text-gray-500">{{ academic_records.count }} course{{ academic_records.count|pluralize }}</span>
                </div>
                
                {% if academic_records %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Credits</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Semester</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for record in academic_records %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ record.course.code }}</div>
                                        <div class="text-sm text-gray-500">{{ record.course.name }}</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        {% if record.grade == 'A' or record.grade == 'A+' %}bg-green-100 text-green-800
                                        {% elif record.grade == 'B' or record.grade == 'B+' %}bg-blue-100 text-blue-800
                                        {% elif record.grade == 'C' or record.grade == 'C+' %}bg-yellow-100 text-yellow-800
                                        {% else %}bg-red-100 text-red-800{% endif %}">
                                        {{ record.grade|default:"N/A" }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ record.course.credits }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ record.semester|default:"N/A" }} {{ record.year|default:"" }}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No academic records</h3>
                    <p class="mt-1 text-sm text-gray-500">This student hasn't enrolled in any courses yet.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
