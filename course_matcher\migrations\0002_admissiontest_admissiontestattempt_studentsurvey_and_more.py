# Generated by Django 5.2.3 on 2025-07-07 11:15

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('course_matcher', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AdmissionTest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question_text', models.TextField()),
                ('question_type', models.Char<PERSON>ield(choices=[('multiple_choice', 'Multiple Choice'), ('true_false', 'True/False'), ('short_answer', 'Short Answer')], max_length=20)),
                ('subject_area', models.CharField(choices=[('mathematics', 'Mathematics'), ('science', 'Science'), ('english', 'English'), ('logical_reasoning', 'Logical Reasoning'), ('general_knowledge', 'General Knowledge')], max_length=30)),
                ('difficulty_level', models.CharField(choices=[('beginner', 'Beginner'), ('intermediate', 'Intermediate'), ('advanced', 'Advanced')], default='intermediate', max_length=20)),
                ('options', models.JSONField(default=list, help_text='List of answer options for multiple choice questions')),
                ('correct_answer', models.TextField()),
                ('explanation', models.TextField(blank=True, help_text='Explanation for the correct answer')),
                ('points', models.IntegerField(default=1, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(10)])),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['subject_area', 'difficulty_level', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='AdmissionTestAttempt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('total_score', models.IntegerField(default=0)),
                ('max_possible_score', models.IntegerField(default=0)),
                ('percentage_score', models.FloatField(default=0.0)),
                ('is_completed', models.BooleanField(default=False)),
                ('time_taken_minutes', models.IntegerField(blank=True, null=True)),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='course_matcher.studentprofile')),
            ],
            options={
                'ordering': ['-started_at'],
            },
        ),
        migrations.CreateModel(
            name='StudentSurvey',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('learning_style', models.CharField(choices=[('visual', 'Visual Learner'), ('auditory', 'Auditory Learner'), ('kinesthetic', 'Kinesthetic Learner'), ('reading_writing', 'Reading/Writing Learner')], max_length=20)),
                ('study_preference', models.CharField(choices=[('individual', 'Individual Study'), ('group', 'Group Study'), ('mixed', 'Mixed Approach')], max_length=20)),
                ('time_preference', models.CharField(choices=[('morning', 'Morning Person'), ('afternoon', 'Afternoon Person'), ('evening', 'Evening Person'), ('night', 'Night Owl')], max_length=20)),
                ('motivation_factors', models.JSONField(default=list, help_text='List of motivation factors')),
                ('preferred_course_format', models.CharField(choices=[('lecture', 'Traditional Lecture'), ('seminar', 'Seminar Style'), ('lab', 'Laboratory/Hands-on'), ('online', 'Online Learning'), ('hybrid', 'Hybrid Format')], default='lecture', max_length=20)),
                ('stress_level', models.IntegerField(help_text='Stress level on a scale of 1-10', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(10)])),
                ('extracurricular_time', models.IntegerField(help_text='Hours per week for extracurricular activities', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(40)])),
                ('work_hours', models.IntegerField(help_text='Hours per week for part-time work', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(40)])),
                ('technology_comfort', models.IntegerField(help_text='Comfort level with technology on a scale of 1-10', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(10)])),
                ('career_certainty', models.IntegerField(help_text='How certain are you about your career goals (1-10)', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(10)])),
                ('additional_comments', models.TextField(blank=True)),
                ('completed_at', models.DateTimeField(auto_now_add=True)),
                ('student', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='course_matcher.studentprofile')),
            ],
            options={
                'ordering': ['-completed_at'],
            },
        ),
        migrations.CreateModel(
            name='AdmissionTestAnswer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('student_answer', models.TextField()),
                ('is_correct', models.BooleanField(default=False)),
                ('points_earned', models.IntegerField(default=0)),
                ('answered_at', models.DateTimeField(auto_now_add=True)),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='course_matcher.admissiontest')),
                ('attempt', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='course_matcher.admissiontestattempt')),
            ],
            options={
                'ordering': ['answered_at'],
                'unique_together': {('attempt', 'question')},
            },
        ),
    ]
