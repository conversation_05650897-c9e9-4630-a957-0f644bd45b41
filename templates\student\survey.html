{% extends 'student/base.html' %}

{% block title %}Student Survey - Course Recommendation System{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Learning Preferences Survey</h1>
        <p class="text-lg text-gray-600">Help us understand your learning style and preferences to provide better course recommendations.</p>
    </div>

    <form method="post" class="space-y-8">
        {% csrf_token %}
        
        <!-- Learning Style Section -->
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                Learning Preferences
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Learning Style -->
                <div>
                    <label for="{{ form.learning_style.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        How do you learn best?
                    </label>
                    {{ form.learning_style }}
                    {% if form.learning_style.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.learning_style.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <!-- Study Preference -->
                <div>
                    <label for="{{ form.study_preference.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Study Preference
                    </label>
                    {{ form.study_preference }}
                    {% if form.study_preference.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.study_preference.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <!-- Time Preference -->
                <div>
                    <label for="{{ form.time_preference.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        When are you most productive?
                    </label>
                    {{ form.time_preference }}
                    {% if form.time_preference.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.time_preference.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <!-- Course Format -->
                <div>
                    <label for="{{ form.preferred_course_format.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Preferred Course Format
                    </label>
                    {{ form.preferred_course_format }}
                    {% if form.preferred_course_format.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.preferred_course_format.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Motivation Factors -->
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                What Motivates You?
            </h2>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-4">
                    Select all factors that motivate you (choose at least 2):
                </label>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {% for choice in form.motivation_factors %}
                    <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors duration-200">
                        {{ choice.tag }}
                        <span class="ml-3 text-gray-700">{{ choice.choice_label }}</span>
                    </label>
                    {% endfor %}
                </div>
                {% if form.motivation_factors.errors %}
                    <p class="mt-2 text-sm text-red-600">{{ form.motivation_factors.errors.0 }}</p>
                {% endif %}
            </div>
        </div>

        <!-- Personal Assessment -->
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                Personal Assessment
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Stress Level -->
                <div>
                    <label for="{{ form.stress_level.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Current Stress Level (1-10)
                    </label>
                    {{ form.stress_level }}
                    <p class="mt-1 text-xs text-gray-500">1 = Very Low Stress, 10 = Very High Stress</p>
                    {% if form.stress_level.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.stress_level.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <!-- Technology Comfort -->
                <div>
                    <label for="{{ form.technology_comfort.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Technology Comfort Level (1-10)
                    </label>
                    {{ form.technology_comfort }}
                    <p class="mt-1 text-xs text-gray-500">1 = Not Comfortable, 10 = Very Comfortable</p>
                    {% if form.technology_comfort.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.technology_comfort.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <!-- Career Certainty -->
                <div>
                    <label for="{{ form.career_certainty.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Career Goals Certainty (1-10)
                    </label>
                    {{ form.career_certainty }}
                    <p class="mt-1 text-xs text-gray-500">1 = Very Uncertain, 10 = Very Certain</p>
                    {% if form.career_certainty.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.career_certainty.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Time Commitments -->
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                <div class="w-8 h-8 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                Time Commitments
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Extracurricular Time -->
                <div>
                    <label for="{{ form.extracurricular_time.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Extracurricular Activities (hours/week)
                    </label>
                    {{ form.extracurricular_time }}
                    <p class="mt-1 text-xs text-gray-500">Include clubs, sports, volunteering, etc.</p>
                    {% if form.extracurricular_time.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.extracurricular_time.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <!-- Work Hours -->
                <div>
                    <label for="{{ form.work_hours.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Part-time Work (hours/week)
                    </label>
                    {{ form.work_hours }}
                    <p class="mt-1 text-xs text-gray-500">Include any paid employment</p>
                    {% if form.work_hours.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.work_hours.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Additional Comments -->
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                <div class="w-8 h-8 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                Additional Comments
            </h2>
            
            <div>
                <label for="{{ form.additional_comments.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Any additional information about your learning preferences or goals?
                </label>
                {{ form.additional_comments }}
                {% if form.additional_comments.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.additional_comments.errors.0 }}</p>
                {% endif %}
            </div>
        </div>

        <!-- Submit Section -->
        <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-6">
            <div class="text-center">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                    {% if existing_survey %}Update Your Survey{% else %}Complete Your Survey{% endif %}
                </h3>
                <p class="text-gray-600 mb-6">
                    {% if existing_survey %}
                        Update your responses to get more accurate course recommendations.
                    {% else %}
                        This information will help us provide personalized course recommendations.
                    {% endif %}
                </p>
                
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{% url 'student_dashboard' %}" 
                       class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Dashboard
                    </a>
                    <button type="submit"
                            class="inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 transition-all duration-200 hover:shadow-lg transform hover:-translate-y-0.5">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {% if existing_survey %}Update Survey{% else %}Submit Survey{% endif %}
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<style>
    .backdrop-blur-sm {
        backdrop-filter: blur(4px);
    }
</style>
{% endblock %}
