{% extends 'base.html' %}

{% block title %}Admission Test Statistics - {{ block.super }}{% endblock %}

{% block sidebar %}
    {% include 'admin/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<div class="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-white/20">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h2 class="text-2xl font-semibold text-gray-800">Admission Test Statistics</h2>
            <p class="text-gray-600 mt-1">Comprehensive analytics and performance metrics</p>
        </div>
        <a href="{% url 'management_admission_tests' %}" 
           class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition-transform transform hover:scale-105 inline-flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Tests
        </a>
    </div>

    <!-- Overall Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200 rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-blue-600">Total Questions</p>
                    <p class="text-3xl font-bold text-blue-900">{{ total_tests }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-gradient-to-r from-green-50 to-green-100 border border-green-200 rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-green-600">Active Questions</p>
                    <p class="text-3xl font-bold text-green-900">{{ active_tests }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-gradient-to-r from-purple-50 to-purple-100 border border-purple-200 rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="w-8 h-8 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-purple-600">Total Attempts</p>
                    <p class="text-3xl font-bold text-purple-900">{{ total_attempts }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-gradient-to-r from-amber-50 to-amber-100 border border-amber-200 rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="w-8 h-8 text-amber-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-amber-600">Completed Tests</p>
                    <p class="text-3xl font-bold text-amber-900">{{ completed_attempts }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Subject Area Statistics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Statistics by Subject Area</h3>
            
            <div class="space-y-4">
                {% for subject_name, stats in subject_stats.items %}
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-3 h-3 rounded-full 
                            {% if 'Mathematics' in subject_name %}bg-blue-500
                            {% elif 'Science' in subject_name %}bg-green-500
                            {% elif 'English' in subject_name %}bg-purple-500
                            {% elif 'History' in subject_name %}bg-amber-500
                            {% else %}bg-gray-500{% endif %}"></div>
                        <span class="font-medium text-gray-900">{{ subject_name }}</span>
                    </div>
                    <div class="text-right">
                        <div class="text-sm text-gray-600">{{ stats.tests_count }} questions</div>
                        <div class="text-sm text-gray-500">{{ stats.attempts_count }} attempts</div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        
        <!-- Difficulty Level Statistics -->
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Statistics by Difficulty Level</h3>
            
            <div class="space-y-4">
                {% for difficulty_name, stats in difficulty_stats.items %}
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-3 h-3 rounded-full 
                            {% if 'Easy' in difficulty_name %}bg-green-500
                            {% elif 'Medium' in difficulty_name %}bg-yellow-500
                            {% else %}bg-red-500{% endif %}"></div>
                        <span class="font-medium text-gray-900">{{ difficulty_name }}</span>
                    </div>
                    <div class="text-right">
                        <div class="text-sm text-gray-600">{{ stats.tests_count }} questions</div>
                        <div class="text-sm text-gray-500">{{ stats.attempts_count }} attempts</div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white rounded-lg border border-gray-200">
        <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Test Completions</h3>
        </div>
        
        {% if recent_attempts %}
        <div class="divide-y divide-gray-200">
            {% for attempt in recent_attempts %}
            <div class="p-6 hover:bg-gray-50 transition-colors duration-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
                                <span class="text-sm font-medium text-white">
                                    {{ attempt.student.user.first_name|first|default:attempt.student.user.username|first|upper }}{{ attempt.student.user.last_name|first|upper }}
                                </span>
                            </div>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900">
                                {{ attempt.student.user.get_full_name|default:attempt.student.user.username }}
                            </p>
                            <p class="text-sm text-gray-500">
                                Student ID: {{ attempt.student.student_id }}
                            </p>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-6">
                        <div class="text-center">
                            <p class="text-sm text-gray-600">Score</p>
                            <p class="text-lg font-semibold text-gray-900">{{ attempt.score|floatformat:1 }}%</p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-gray-600">Date</p>
                            <p class="text-sm font-medium text-gray-900">{{ attempt.date_taken|date:"M d, Y" }}</p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-gray-600">Time</p>
                            <p class="text-sm font-medium text-gray-900">{{ attempt.date_taken|date:"H:i" }}</p>
                        </div>
                        <div class="flex-shrink-0">
                            {% if attempt.score >= 80 %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Excellent
                            </span>
                            {% elif attempt.score >= 60 %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                Good
                            </span>
                            {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Needs Improvement
                            </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="p-8 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No completed tests yet</h3>
            <p class="mt-1 text-sm text-gray-500">Test statistics will appear here once students start completing admission tests.</p>
        </div>
        {% endif %}
    </div>

    <!-- Performance Insights -->
    <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 class="text-lg font-medium text-blue-900 mb-4">Performance Insights</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white rounded-lg p-4 border border-blue-200">
                <h4 class="text-sm font-medium text-blue-800 mb-2">Test Coverage</h4>
                <p class="text-2xl font-bold text-blue-900">{{ active_tests }}</p>
                <p class="text-sm text-blue-700">Active questions available</p>
            </div>
            
            <div class="bg-white rounded-lg p-4 border border-blue-200">
                <h4 class="text-sm font-medium text-blue-800 mb-2">Student Engagement</h4>
                <p class="text-2xl font-bold text-blue-900">{{ completed_attempts }}</p>
                <p class="text-sm text-blue-700">Tests completed</p>
            </div>
            
            <div class="bg-white rounded-lg p-4 border border-blue-200">
                <h4 class="text-sm font-medium text-blue-800 mb-2">Completion Rate</h4>
                <p class="text-2xl font-bold text-blue-900">
                    {% if total_attempts > 0 %}
                        {{ completed_attempts|floatformat:0 }}/{{ total_attempts }}
                    {% else %}
                        0/0
                    {% endif %}
                </p>
                <p class="text-sm text-blue-700">
                    {% if total_attempts > 0 %}
                        {{ completed_attempts|mul:100|div:total_attempts|floatformat:1 }}% completion
                    {% else %}
                        No data yet
                    {% endif %}
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
