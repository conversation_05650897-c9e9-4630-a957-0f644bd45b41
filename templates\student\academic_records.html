{% extends 'student/base.html' %}

{% block title %}Academic Records - Course Recommendation System{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8" 
     x-data="academicRecords()" 
     x-init="init()">
    
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Academic Records</h1>
        <p class="mt-2 text-gray-600">Manage your academic history, including courses taken, grades received, and overall GPA.</p>
    </div>

    <!-- Course History Section -->
    <div class="bg-white rounded-lg shadow mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Course History</h2>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course Name</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course Code</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Credits</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Semester</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id="course-history-tbody">
                    {% for record in records %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {{ record.course.name }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 font-medium">
                            {{ record.course.code }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ record.course.credits }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            {% if record.grade %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                           {% if record.grade in 'A,A-' %}bg-green-100 text-green-800
                                           {% elif record.grade in 'B+,B,B-' %}bg-blue-100 text-blue-800
                                           {% elif record.grade in 'C+,C,C-' %}bg-yellow-100 text-yellow-800
                                           {% else %}bg-red-100 text-red-800{% endif %}">
                                    {{ record.grade }}
                                </span>
                            {% else %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                    In Progress
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ record.get_semester_display }} {{ record.year }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <button hx-delete="{% url 'delete_academic_record' record.id %}"
                                    hx-confirm="Are you sure you want to delete this record?"
                                    hx-target="closest tr"
                                    hx-swap="outerHTML"
                                    class="text-red-600 hover:text-red-900 transition-colors">
                                Delete
                            </button>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                            No academic records found. Add your first course below!
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- GPA Summary -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Overall GPA</dt>
                        <dd class="text-2xl font-bold text-gray-900">{{ gpa|floatformat:2|default:"N/A" }}</dd>
                    </dl>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M12 6V4l-8 8v2h2l8-8zm2-2l2-2v2h-2z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Credits Earned</dt>
                        <dd class="text-2xl font-bold text-gray-900">{{ total_credits|default:"0" }}</dd>
                    </dl>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Courses Completed</dt>
                        <dd class="text-2xl font-bold text-gray-900">{{ records.count }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Add New Course Section -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Add New Course</h2>
        </div>
        
        <form method="post" class="p-6 space-y-6">
            {% csrf_token %}

            {% if form.non_field_errors %}
                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">
                                Please correct the following errors:
                            </h3>
                            <div class="mt-2 text-sm text-red-700">
                                {{ form.non_field_errors }}
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label for="{{ form.course.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Course
                    </label>
                    {{ form.course }}
                    {% if form.course.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.course.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.semester.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Semester
                    </label>
                    {{ form.semester }}
                    {% if form.semester.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.semester.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.year.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Year
                    </label>
                    {{ form.year }}
                    {% if form.year.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.year.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.grade.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Grade (Optional)
                    </label>
                    {{ form.grade }}
                    {% if form.grade.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.grade.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>

            <div class="flex justify-end">
                <button type="submit"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Course
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function academicRecords() {
    return {
        records: {{ records|length }},

        init() {
            // Initialize any needed functionality
            console.log('Academic records initialized');
        }
    };
}
</script>
{% endblock %}

{% block extra_css %}
<style>
    .htmx-request .htmx-indicator {
        opacity: 1;
    }
    
    .htmx-indicator {
        opacity: 0;
        transition: opacity 0.3s ease;
    }
</style>
{% endblock %}
