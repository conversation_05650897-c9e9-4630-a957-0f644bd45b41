{% extends 'admin/base.html' %}

{% block title %}{{ action }} Academic Major - Admin Portal{% endblock %}

{% block content %}
<div class="flex-1 p-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center space-x-4 mb-4">
            <a href="{% url 'management_departments' %}" 
               class="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Academic Majors
            </a>
        </div>
        <h1 class="text-3xl font-bold text-gray-900">{{ action }} Academic Major</h1>
        <p class="mt-2 text-gray-600">
            {% if action == 'Create' %}
            Add a new academic major/department for students to select from.
            {% else %}
            Update the academic major/department information.
            {% endif %}
        </p>
    </div>

    <!-- Form -->
    <div class="max-w-2xl">
        <div class="glassmorphism rounded-xl p-8">
            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <!-- Department Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        Department Name *
                    </label>
                    <input type="text" 
                           id="name" 
                           name="name" 
                           value="{% if department %}{{ department.name }}{% endif %}"
                           required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                           placeholder="e.g., Computer Science">
                    <p class="mt-1 text-sm text-gray-500">The full name of the academic department</p>
                </div>

                <!-- Department Code -->
                <div>
                    <label for="code" class="block text-sm font-medium text-gray-700 mb-2">
                        Department Code *
                    </label>
                    <input type="text" 
                           id="code" 
                           name="code" 
                           value="{% if department %}{{ department.code }}{% endif %}"
                           required
                           maxlength="10"
                           style="text-transform: uppercase;"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                           placeholder="e.g., CS">
                    <p class="mt-1 text-sm text-gray-500">Short code for the department (will be converted to uppercase)</p>
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                        Description
                    </label>
                    <textarea id="description" 
                              name="description" 
                              rows="4"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                              placeholder="Brief description of the department and its focus areas...">{% if department %}{{ department.description }}{% endif %}</textarea>
                    <p class="mt-1 text-sm text-gray-500">Optional description to help students understand this major</p>
                </div>

                <!-- Preview Section -->
                {% if department %}
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-blue-800 mb-2">Current Statistics</h4>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-blue-600">Courses:</span>
                            <span class="font-medium text-blue-800">{{ department.course_set.count }}</span>
                        </div>
                        <div>
                            <span class="text-blue-600">Students:</span>
                            <span class="font-medium text-blue-800">{{ department.studentprofile_set.count }}</span>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                    <a href="{% url 'management_departments' %}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200">
                        Cancel
                    </a>
                    
                    <button type="submit" 
                            class="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 transition-all duration-200 shadow-lg">
                        {% if action == 'Create' %}
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Create Major
                        {% else %}
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Update Major
                        {% endif %}
                    </button>
                </div>
            </form>
        </div>

        <!-- Help Section -->
        <div class="mt-8 glassmorphism rounded-xl p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Guidelines</h3>
            <div class="space-y-3 text-sm text-gray-600">
                <div class="flex items-start">
                    <svg class="w-5 h-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                    <div>
                        <strong>Department Name:</strong> Use the full, official name of the academic department (e.g., "Computer Science", "Business Administration")
                    </div>
                </div>
                <div class="flex items-start">
                    <svg class="w-5 h-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                    <div>
                        <strong>Department Code:</strong> Keep it short and memorable (2-5 characters). This will be used in course codes and student records.
                    </div>
                </div>
                <div class="flex items-start">
                    <svg class="w-5 h-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                    <div>
                        <strong>Description:</strong> Provide a brief overview that helps students understand what this major covers and its career prospects.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-uppercase the code field
document.getElementById('code').addEventListener('input', function(e) {
    e.target.value = e.target.value.toUpperCase();
});
</script>
{% endblock %}
