{% extends 'base.html' %}

{% block title %}Delete Student - {{ student.user.get_full_name }} - {{ block.super }}{% endblock %}

{% block sidebar %}
    {% include 'admin/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-8">
        <!-- Warning Icon -->
        <div class="flex items-center justify-center w-16 h-16 mx-auto bg-red-100 rounded-full mb-6">
            <svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
            </svg>
        </div>

        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-2xl font-bold text-gray-900 mb-2">Delete Student</h1>
            <p class="text-gray-600">Are you sure you want to delete this student? This action cannot be undone.</p>
        </div>

        <!-- Student Information -->
        <div class="bg-gray-50 rounded-lg p-6 mb-8">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                    <span class="text-lg font-bold text-primary-600">
                        {{ student.user.first_name|first }}{{ student.user.last_name|first }}
                    </span>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">{{ student.user.get_full_name }}</h3>
                    <p class="text-gray-600">{{ student.student_id }}</p>
                    {% if student.major %}
                    <p class="text-sm text-gray-500">{{ student.major.name }}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Warning Message -->
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-8">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="ml-3">
                    <h4 class="text-sm font-medium text-red-800">Warning</h4>
                    <div class="mt-1 text-sm text-red-700">
                        <ul class="list-disc list-inside space-y-1">
                            <li>This will permanently delete the student's account and profile</li>
                            <li>All academic records associated with this student will be removed</li>
                            <li>Any recommendations or advising sessions will be deleted</li>
                            <li>This action cannot be undone</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <form method="post" class="flex items-center justify-end space-x-4">
            {% csrf_token %}
            <a href="{% url 'admin_student_view' student.id %}" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                Cancel
            </a>
            <button type="submit" class="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                Delete Student
            </button>
        </form>
    </div>
</div>
{% endblock %}
