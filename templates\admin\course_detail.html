{% extends 'base.html' %}

{% block title %}{{ course.code }} - {{ course.name }} - Course Details - {{ block.super }}{% endblock %}

{% block sidebar %}
    {% include 'admin/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto space-y-6">
    <!-- Header -->
    <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
        <div class="flex items-center justify-between">
            <div>
                <div class="flex items-center space-x-3 mb-2">
                    <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-primary-100 text-primary-800">
                        {{ course.code }}
                    </span>
                    <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full 
                        {% if course.difficulty == 'beginner' %}bg-green-100 text-green-800
                        {% elif course.difficulty == 'intermediate' %}bg-yellow-100 text-yellow-800
                        {% else %}bg-red-100 text-red-800{% endif %}">
                        {{ course.get_difficulty_display }}
                    </span>
                </div>
                <h1 class="text-3xl font-bold text-gray-900">{{ course.name }}</h1>
                <p class="text-gray-600 mt-1">{{ course.department.name }} • {{ course.credits }} Credit{{ course.credits|pluralize }}</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'admin_course_edit' course.id %}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors inline-flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
                    </svg>
                    Edit Course
                </a>
                <a href="{% url 'management_courses' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors inline-flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"/>
                    </svg>
                    Back to Courses
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Course Information -->
        <div class="lg:col-span-1">
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Course Information</h2>
                <div class="space-y-4">
                    <div>
                        <label class="text-sm font-medium text-gray-500">Course Code</label>
                        <p class="text-gray-900 font-mono">{{ course.code }}</p>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-500">Department</label>
                        <p class="text-gray-900">{{ course.department.name }}</p>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-500">Credits</label>
                        <p class="text-gray-900">{{ course.credits }}</p>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-500">Difficulty Level</label>
                        <p class="text-gray-900">{{ course.get_difficulty_display }}</p>
                    </div>
                    {% if course.topics %}
                    <div>
                        <label class="text-sm font-medium text-gray-500">Topics</label>
                        <div class="flex flex-wrap gap-1 mt-1">
                            {% for topic in course.topics %}
                            <span class="inline-flex px-2 py-1 text-xs font-medium rounded-md bg-blue-100 text-blue-800">
                                {{ topic }}
                            </span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Enrollment Statistics -->
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 mt-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Enrollment Statistics</h2>
                <div class="space-y-4">
                    <div>
                        <label class="text-sm font-medium text-gray-500">Total Enrolled</label>
                        <p class="text-2xl font-bold text-primary-600">{{ enrolled_students.count }}</p>
                    </div>
                    {% if enrolled_students %}
                    <div>
                        <label class="text-sm font-medium text-gray-500">Average Grade</label>
                        <p class="text-lg font-semibold text-gray-900">
                            {% with avg_grade=enrolled_students|length %}
                                {% if avg_grade > 0 %}B+{% else %}N/A{% endif %}
                            {% endwith %}
                        </p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Course Description and Enrolled Students -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Course Description -->
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Course Description</h2>
                <div class="prose prose-gray max-w-none">
                    <p class="text-gray-700 leading-relaxed">{{ course.description|linebreaks }}</p>
                </div>
            </div>

            <!-- Enrolled Students -->
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-gray-800">Enrolled Students</h2>
                    <span class="text-sm text-gray-500">{{ enrolled_students.count }} student{{ enrolled_students.count|pluralize }}</span>
                </div>
                
                {% if enrolled_students %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Year</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Semester</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for record in enrolled_students %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-sm font-medium text-primary-600">
                                                {{ record.student.user.first_name|first }}{{ record.student.user.last_name|first }}
                                            </span>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{{ record.student.user.get_full_name }}</div>
                                            <div class="text-sm text-gray-500">{{ record.student.user.email }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ record.student.student_id }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ record.student.get_year_display|default:"N/A" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        {% if record.grade == 'A' or record.grade == 'A+' %}bg-green-100 text-green-800
                                        {% elif record.grade == 'B' or record.grade == 'B+' %}bg-blue-100 text-blue-800
                                        {% elif record.grade == 'C' or record.grade == 'C+' %}bg-yellow-100 text-yellow-800
                                        {% else %}bg-red-100 text-red-800{% endif %}">
                                        {{ record.grade|default:"In Progress" }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ record.semester|default:"N/A" }} {{ record.year|default:"" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="{% url 'admin_student_view' record.student.id %}" class="text-primary-600 hover:text-primary-900">View Student</a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No enrolled students</h3>
                    <p class="mt-1 text-sm text-gray-500">No students are currently enrolled in this course.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
