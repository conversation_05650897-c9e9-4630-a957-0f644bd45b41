from django.core.management.base import BaseCommand
from course_matcher.models import Course, Department
from course_matcher.recommendation_service import RecommendationEngine
from course_matcher.models import StudentProfile


class Command(BaseCommand):
    help = 'Set up test data for course recommendations'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up test data...'))
        
        # Create department
        dept, created = Department.objects.get_or_create(
            code='CS',
            defaults={'name': 'Computer Science'}
        )
        if created:
            self.stdout.write(f'Created department: {dept}')
        
        # Create multiple courses for better recommendations
        courses_data = [
            ('CS101', 'Introduction to Programming', 'Basic programming concepts using Python. Learn variables, loops, functions, and basic data structures.', 'beginner', ['python', 'programming', 'basics', 'variables', 'functions']),
            ('CS201', 'Data Structures', 'Advanced data structures and algorithms. Study arrays, linked lists, trees, graphs, and sorting algorithms.', 'intermediate', ['algorithms', 'data structures', 'trees', 'graphs', 'sorting']),
            ('CS301', 'Machine Learning', 'Introduction to machine learning concepts. Cover supervised learning, neural networks, and data analysis.', 'advanced', ['machine learning', 'ai', 'neural networks', 'data science', 'python']),
            ('CS302', 'Web Development', 'Full-stack web development using modern frameworks. Learn HTML, CSS, JavaScript, and backend development.', 'intermediate', ['web development', 'javascript', 'html', 'css', 'frontend', 'backend']),
            ('CS401', 'Artificial Intelligence', 'AI principles and applications. Study search algorithms, knowledge representation, and expert systems.', 'advanced', ['artificial intelligence', 'ai', 'search algorithms', 'expert systems', 'logic']),
            ('CS202', 'Database Systems', 'Database design and management. Learn SQL, normalization, and database administration.', 'intermediate', ['database', 'sql', 'data management', 'normalization', 'queries']),
            ('CS303', 'Software Engineering', 'Software development methodologies and project management. Study agile, testing, and version control.', 'intermediate', ['software engineering', 'agile', 'testing', 'project management', 'version control']),
            ('CS402', 'Computer Networks', 'Network protocols and distributed systems. Learn TCP/IP, routing, and network security.', 'advanced', ['networking', 'protocols', 'tcp/ip', 'security', 'distributed systems']),
        ]

        created_courses = []
        for code, name, desc, difficulty, topics in courses_data:
            course, created = Course.objects.get_or_create(
                code=code,
                defaults={
                    'name': name,
                    'description': desc,
                    'credits': 3,
                    'department': dept,
                    'difficulty': difficulty,
                    'topics': topics
                }
            )
            if created:
                self.stdout.write(f'Created course: {course}')
            created_courses.append(course)
        
        self.stdout.write(f'Total courses in database: {Course.objects.count()}')
        
        # Generate recommendations for all students with complete profiles
        engine = RecommendationEngine()
        students_with_complete_profiles = []
        
        for student in StudentProfile.objects.all():
            from course_matcher.views import get_profile_completion_status
            status = get_profile_completion_status(student)
            if all(status.values()):
                students_with_complete_profiles.append(student)
        
        self.stdout.write(f'Found {len(students_with_complete_profiles)} students with complete profiles')
        
        for student in students_with_complete_profiles:
            try:
                recommendations = engine.get_recommendations(student)
                self.stdout.write(f'Generated {len(recommendations)} recommendations for {student.user.first_name} {student.user.last_name}')
                
                # Show top 3 recommendations
                for i, rec in enumerate(recommendations[:3], 1):
                    self.stdout.write(f'  #{i}: {rec.course.code} - {rec.confidence_score:.1f}%')
            except Exception as e:
                self.stdout.write(f'Error generating recommendations for {student}: {e}')
        
        self.stdout.write(self.style.SUCCESS('Test data setup completed!'))
