from django.core.management.base import BaseCommand
from django.test import Client
from django.contrib.auth.models import User
from course_matcher.models import Department, StudentProfile, Course, AcademicRecord, AdmissionTestAttempt, StudentSurvey
from course_matcher.views import get_profile_completion_status
from course_matcher.recommendation_service import RecommendationEngine


class Command(BaseCommand):
    help = 'Test comprehensive profile completion system integration'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Testing Profile Completion System Integration...'))
        
        # Test 1: Admin Department Management
        self.test_admin_department_management()
        
        # Test 2: Student Profile Completion Flow
        self.test_student_profile_completion()
        
        # Test 3: Major Selection Integration
        self.test_major_selection_integration()
        
        # Test 4: Recommendation Engine Integration
        self.test_recommendation_integration()
        
        # Test 5: Profile Completion Validation
        self.test_profile_completion_validation()
        
        self.stdout.write(self.style.SUCCESS('✅ All integration tests passed!'))

    def test_admin_department_management(self):
        """Test admin department CRUD operations"""
        self.stdout.write('\n--- Test 1: Admin Department Management ---')
        
        client = Client()
        
        # Create admin user
        admin_user = User.objects.create_superuser(
            username='test_admin',
            email='<EMAIL>',
            password='admin123'
        )
        client.login(username='test_admin', password='admin123')
        
        # Test department list page
        response = client.get('/management/departments/')
        self.stdout.write(f'Department list page: {response.status_code}')
        assert response.status_code == 200, "Department list page should be accessible"
        
        # Test department creation
        response = client.post('/management/departments/create/', {
            'name': 'Test Department',
            'code': 'TEST',
            'description': 'A test department for integration testing'
        })
        self.stdout.write(f'Department creation: {response.status_code}')
        assert response.status_code == 302, "Department creation should redirect"
        
        # Verify department was created
        dept = Department.objects.get(code='TEST')
        assert dept.name == 'Test Department'
        self.stdout.write('✅ Department CRUD operations working')

    def test_student_profile_completion(self):
        """Test student profile completion interface"""
        self.stdout.write('\n--- Test 2: Student Profile Completion Flow ---')
        
        client = Client()
        
        # Create test student
        user = User.objects.create_user(
            username='test_student_integration',
            email='<EMAIL>',
            password='student123',
            first_name='Test',
            last_name='Student'
        )
        
        student = StudentProfile.objects.create(
            user=user,
            student_id='TESTINT001',
            year='sophomore'
        )
        
        client.login(username='test_student_integration', password='student123')
        
        # Test profile completion page
        response = client.get('/profile-completion/')
        self.stdout.write(f'Profile completion page: {response.status_code}')
        assert response.status_code == 200, "Profile completion page should be accessible"
        
        # Check initial completion status
        status = get_profile_completion_status(student)
        initial_completed = sum(status.values())
        self.stdout.write(f'Initial completion: {initial_completed}/6')
        
        self.stdout.write('✅ Profile completion interface working')

    def test_major_selection_integration(self):
        """Test major selection functionality"""
        self.stdout.write('\n--- Test 3: Major Selection Integration ---')
        
        client = Client()
        
        # Get test student
        user = User.objects.get(username='test_student_integration')
        student = user.studentprofile
        client.login(username='test_student_integration', password='student123')
        
        # Test major selection page
        response = client.get('/select-major/')
        self.stdout.write(f'Major selection page: {response.status_code}')
        assert response.status_code == 200, "Major selection page should be accessible"
        
        # Test major selection
        dept = Department.objects.first()
        if dept:
            response = client.post('/select-major/', {
                'major': dept.id
            })
            self.stdout.write(f'Major selection: {response.status_code}')
            assert response.status_code == 302, "Major selection should redirect"
            
            # Verify major was set
            student.refresh_from_db()
            assert student.major == dept, "Major should be set correctly"
            self.stdout.write(f'✅ Major selection working - selected {dept.name}')
        else:
            self.stdout.write('⚠️ No departments available for major selection test')

    def test_recommendation_integration(self):
        """Test recommendation engine integration with profile completion"""
        self.stdout.write('\n--- Test 4: Recommendation Engine Integration ---')
        
        # Get test student
        user = User.objects.get(username='test_student_integration')
        student = user.studentprofile
        
        # Complete the profile step by step
        self.complete_student_profile(student)
        
        # Check final completion status
        status = get_profile_completion_status(student)
        completed_count = sum(status.values())
        self.stdout.write(f'Final completion: {completed_count}/6')
        
        if completed_count == 6:
            # Test recommendation generation
            engine = RecommendationEngine()
            recommendations = engine.get_recommendations(student)
            self.stdout.write(f'Generated recommendations: {len(recommendations)}')
            
            # Test recommendations page
            client = Client()
            client.login(username='test_student_integration', password='student123')
            response = client.get('/recommendations/')
            self.stdout.write(f'Recommendations page: {response.status_code}')
            assert response.status_code == 200, "Recommendations page should be accessible"
            
            self.stdout.write('✅ Recommendation integration working')
        else:
            self.stdout.write('⚠️ Profile not complete, skipping recommendation test')

    def test_profile_completion_validation(self):
        """Test profile completion validation logic"""
        self.stdout.write('\n--- Test 5: Profile Completion Validation ---')
        
        # Create a new test student for validation testing
        user = User.objects.create_user(
            username='test_validation',
            email='<EMAIL>',
            password='test123'
        )
        
        student = StudentProfile.objects.create(
            user=user,
            student_id='TESTVAL001',
            year='freshman'
        )
        
        # Test each component individually
        status = get_profile_completion_status(student)
        self.stdout.write(f'Empty profile: {sum(status.values())}/6')
        assert sum(status.values()) == 0, "Empty profile should have 0 completed components"
        
        # Add interests
        student.interests = ['Programming', 'Data Science']
        student.save()
        status = get_profile_completion_status(student)
        self.stdout.write(f'With interests: {sum(status.values())}/6')
        assert status['interests'] == True, "Interests should be marked as complete"
        
        # Add career goals
        student.career_goals = 'I want to become a software engineer'
        student.save()
        status = get_profile_completion_status(student)
        self.stdout.write(f'With career goals: {sum(status.values())}/6')
        assert status['career_goals'] == True, "Career goals should be marked as complete"
        
        # Add major
        dept = Department.objects.first()
        if dept:
            student.major = dept
            student.save()
            status = get_profile_completion_status(student)
            self.stdout.write(f'With major: {sum(status.values())}/6')
            assert status['major'] == True, "Major should be marked as complete"
        
        self.stdout.write('✅ Profile completion validation working')

    def complete_student_profile(self, student):
        """Helper method to complete a student's profile"""
        # Add interests
        student.interests = ['Machine Learning', 'Web Development', 'Data Science']
        
        # Add career goals
        student.career_goals = 'I want to become a software engineer specializing in AI and machine learning.'
        
        # Set major
        dept = Department.objects.first()
        if dept:
            student.major = dept
        
        student.save()
        
        # Add academic record
        course = Course.objects.first()
        if course:
            AcademicRecord.objects.get_or_create(
                student=student,
                course=course,
                defaults={
                    'semester': 'fall',
                    'year': 2024,
                    'grade': 'A'
                }
            )
        
        # Add admission test attempt
        AdmissionTestAttempt.objects.get_or_create(
            student=student,
            defaults={
                'is_completed': True,
                'total_score': 85,
                'max_possible_score': 100,
                'percentage_score': 85.0
            }
        )
        
        # Add survey
        StudentSurvey.objects.get_or_create(
            student=student,
            defaults={
                'learning_style': 'visual',
                'study_preference': 'individual',
                'time_preference': 'morning',
                'motivation_factors': ['career_advancement', 'personal_interest'],
                'stress_level': 5,
                'extracurricular_time': 10,
                'work_hours': 15,
                'technology_comfort': 8,
                'career_certainty': 7
            }
        )
        
        self.stdout.write(f'Completed profile for {student.user.get_full_name()}')
