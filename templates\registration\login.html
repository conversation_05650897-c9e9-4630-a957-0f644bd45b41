{% extends 'auth_base.html' %}

{% block title %}Login - CourseRec{% endblock %}

{% block extra_css %}
<style>
    .login-form-container {
        background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.1);
    }

    .floating-label {
        transition: all 0.3s ease;
    }

    .form-input:focus + .floating-label,
    .form-input:not(:placeholder-shown) + .floating-label {
        transform: translateY(-1.5rem) scale(0.85);
        color: #2563eb;
    }

    .password-toggle {
        cursor: pointer;
        transition: color 0.2s ease;
    }

    .password-toggle:hover {
        color: #2563eb;
    }

    .login-animation {
        animation: slideInUp 0.6s ease-out;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .btn-loading {
        position: relative;
        color: transparent;
    }

    .btn-loading::after {
        content: "";
        position: absolute;
        width: 16px;
        height: 16px;
        top: 50%;
        left: 50%;
        margin-left: -8px;
        margin-top: -8px;
        border: 2px solid #ffffff;
        border-radius: 50%;
        border-top-color: transparent;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen lg:grid lg:grid-cols-12 bg-gradient-to-br from-blue-50 via-white to-purple-50">
    <div class="lg:col-span-7 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8 login-animation">
            <!-- Logo/Brand Section -->
            <div class="text-center">
                <div class="mx-auto h-16 w-16 bg-gradient-to-r from-primary-600 to-purple-600 rounded-full flex items-center justify-center mb-4">
                    <svg class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                </div>
                <h2 class="text-3xl font-extrabold text-gray-900 mb-2">
                    Welcome Back
                </h2>
                <p class="text-sm text-gray-600">
                    Sign in to continue your learning journey
                </p>
                <p class="mt-2 text-center text-sm text-gray-600">
                    Don't have an account?
                    <a href="{% url 'student_registration' %}" class="font-medium text-primary-600 hover:text-primary-500 transition-colors duration-200">
                        Create one here
                    </a>
                </p>
            </div>
            <!-- Enhanced Form -->
            <div class="login-form-container rounded-2xl p-8 shadow-xl">
                <form class="space-y-6" method="POST" action="{% url 'login' %}" id="login-form">
                    {% csrf_token %}

                    <!-- Error Messages -->
                    {% if form.errors or form.non_field_errors %}
                        <div class="bg-red-50 border-l-4 border-red-400 p-4 rounded-lg animate-pulse" role="alert">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-red-700 font-medium">
                                        Invalid username or password. Please try again.
                                    </p>
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    <!-- Username Field -->
                    <div class="relative">
                        <input type="text"
                               name="username"
                               id="id_username"
                               class="form-input w-full px-4 py-3 border-2 border-gray-200 rounded-lg text-gray-700 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 placeholder-transparent peer"
                               placeholder="Username"
                               required>
                        <label for="id_username"
                               class="floating-label absolute left-4 top-3 text-gray-500 text-sm transition-all duration-200 pointer-events-none peer-placeholder-shown:top-3 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400 peer-focus:top-0 peer-focus:text-xs peer-focus:text-primary-600 peer-focus:bg-white peer-focus:px-1 peer-focus:-ml-1">
                            Username
                        </label>
                    </div>

                    <!-- Password Field -->
                    <div class="relative">
                        <input type="password"
                               name="password"
                               id="id_password"
                               class="form-input w-full px-4 py-3 pr-12 border-2 border-gray-200 rounded-lg text-gray-700 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 placeholder-transparent peer"
                               placeholder="Password"
                               required>
                        <label for="id_password"
                               class="floating-label absolute left-4 top-3 text-gray-500 text-sm transition-all duration-200 pointer-events-none peer-placeholder-shown:top-3 peer-placeholder-shown:text-base peer-placeholder-shown:text-gray-400 peer-focus:top-0 peer-focus:text-xs peer-focus:text-primary-600 peer-focus:bg-white peer-focus:px-1 peer-focus:-ml-1">
                            Password
                        </label>
                        <button type="button"
                                class="password-toggle absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                                onclick="togglePassword()">
                            <svg class="h-5 w-5" id="eye-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                        </button>
                    </div>

                    <!-- Remember Me & Forgot Password -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <input id="remember-me"
                                   name="remember-me"
                                   type="checkbox"
                                   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                            <label for="remember-me" class="ml-2 block text-sm text-gray-700">
                                Remember me
                            </label>
                        </div>
                        <div class="text-sm">
                            <a href="#" class="font-medium text-primary-600 hover:text-primary-500 transition-colors duration-200">
                                Forgot password?
                            </a>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div>
                        <button type="submit"
                                id="login-btn"
                                class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-primary-600 to-purple-600 hover:from-primary-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                                <svg class="h-5 w-5 text-primary-300 group-hover:text-primary-200 transition-colors duration-200" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                                </svg>
                            </span>
                            <span id="btn-text">Sign In</span>
                        </button>
                    </div>

                    <input type="hidden" name="next" value="{{ next }}" />
                </form>
            </div>
        </div>
    </div>
    <div class="hidden lg:block lg:col-span-5 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1523240795612-9a054b0db644?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80');">
        <div class="h-full bg-primary-800 bg-opacity-60 flex flex-col justify-between p-12">
            <div>
                <h2 class="text-3xl font-bold text-white">Welcome Back</h2>
                <p class="text-primary-200 mt-2">Let's find the right courses for you.</p>
            </div>
            <div>
                <p class="text-white text-lg font-medium">“An investment in knowledge pays the best interest.”</p>
                <p class="text-primary-300 mt-2">— Benjamin Franklin</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function togglePassword() {
        const passwordInput = document.getElementById('id_password');
        const eyeIcon = document.getElementById('eye-icon');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            eyeIcon.innerHTML = `
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
            `;
        } else {
            passwordInput.type = 'password';
            eyeIcon.innerHTML = `
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            `;
        }
    }

    // Form submission with loading state
    document.getElementById('login-form').addEventListener('submit', function(e) {
        const submitBtn = document.getElementById('login-btn');
        const btnText = document.getElementById('btn-text');

        submitBtn.classList.add('btn-loading');
        submitBtn.disabled = true;
        btnText.textContent = 'Signing In...';

        // Re-enable button after 5 seconds in case of error
        setTimeout(() => {
            submitBtn.classList.remove('btn-loading');
            submitBtn.disabled = false;
            btnText.textContent = 'Sign In';
        }, 5000);
    });

    // Enhanced form validation
    document.addEventListener('DOMContentLoaded', function() {
        const usernameInput = document.getElementById('id_username');
        const passwordInput = document.getElementById('id_password');

        function validateField(field) {
            const value = field.value.trim();
            if (value === '') {
                field.classList.add('border-red-300');
                field.classList.remove('border-green-300');
                return false;
            } else {
                field.classList.add('border-green-300');
                field.classList.remove('border-red-300');
                return true;
            }
        }

        usernameInput.addEventListener('blur', () => validateField(usernameInput));
        passwordInput.addEventListener('blur', () => validateField(passwordInput));
    });
</script>
{% endblock %}

