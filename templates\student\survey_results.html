{% extends 'student/base.html' %}

{% block title %}Survey Results - Course Recommendation System{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Your Learning Profile</h1>
        <p class="text-lg text-gray-600">Based on your survey responses, here's your personalized learning profile and insights.</p>
    </div>

    <!-- Learning Profile Overview -->
    <div class="bg-gradient-to-r from-primary-50 to-primary-100 rounded-xl shadow-lg border border-primary-200 p-8 mb-8">
        <div class="text-center mb-6">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full mb-4">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                </svg>
            </div>
            <h2 class="text-2xl font-bold text-primary-800 mb-2">Your Learning Profile</h2>
            <p class="text-primary-700">Completed on {{ survey.completed_at|date:"F d, Y" }}</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="text-center p-4 bg-white/50 rounded-lg">
                <div class="text-lg font-semibold text-primary-800">Learning Style</div>
                <div class="text-xl font-bold text-primary-600">{{ survey.get_learning_style_display }}</div>
            </div>
            <div class="text-center p-4 bg-white/50 rounded-lg">
                <div class="text-lg font-semibold text-primary-800">Study Preference</div>
                <div class="text-xl font-bold text-primary-600">{{ survey.get_study_preference_display }}</div>
            </div>
            <div class="text-center p-4 bg-white/50 rounded-lg">
                <div class="text-lg font-semibold text-primary-800">Best Time</div>
                <div class="text-xl font-bold text-primary-600">{{ survey.get_time_preference_display }}</div>
            </div>
            <div class="text-center p-4 bg-white/50 rounded-lg">
                <div class="text-lg font-semibold text-primary-800">Course Format</div>
                <div class="text-xl font-bold text-primary-600">{{ survey.get_preferred_course_format_display }}</div>
            </div>
        </div>
    </div>

    <!-- Detailed Analysis -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Personal Assessment -->
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
            <h3 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                Personal Assessment
            </h3>
            
            <div class="space-y-4">
                <!-- Stress Level -->
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span class="font-medium text-gray-700">Stress Level</span>
                    <div class="flex items-center">
                        <div class="w-24 bg-gray-200 rounded-full h-2 mr-3">
                            <div class="{% if survey.stress_level <= 3 %}bg-green-500{% elif survey.stress_level <= 6 %}bg-yellow-500{% else %}bg-red-500{% endif %} h-2 rounded-full" 
                                 style="width: {{ survey.stress_level }}0%"></div>
                        </div>
                        <span class="font-bold text-gray-900">{{ survey.stress_level }}/10</span>
                    </div>
                </div>
                
                <!-- Technology Comfort -->
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span class="font-medium text-gray-700">Technology Comfort</span>
                    <div class="flex items-center">
                        <div class="w-24 bg-gray-200 rounded-full h-2 mr-3">
                            <div class="{% if survey.technology_comfort >= 8 %}bg-green-500{% elif survey.technology_comfort >= 5 %}bg-yellow-500{% else %}bg-red-500{% endif %} h-2 rounded-full" 
                                 style="width: {{ survey.technology_comfort }}0%"></div>
                        </div>
                        <span class="font-bold text-gray-900">{{ survey.technology_comfort }}/10</span>
                    </div>
                </div>
                
                <!-- Career Certainty -->
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span class="font-medium text-gray-700">Career Certainty</span>
                    <div class="flex items-center">
                        <div class="w-24 bg-gray-200 rounded-full h-2 mr-3">
                            <div class="{% if survey.career_certainty >= 8 %}bg-green-500{% elif survey.career_certainty >= 5 %}bg-yellow-500{% else %}bg-red-500{% endif %} h-2 rounded-full" 
                                 style="width: {{ survey.career_certainty }}0%"></div>
                        </div>
                        <span class="font-bold text-gray-900">{{ survey.career_certainty }}/10</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Time Commitments -->
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
            <h3 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                <div class="w-8 h-8 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                Time Commitments
            </h3>
            
            <div class="space-y-4">
                <div class="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600">{{ survey.extracurricular_time }}</div>
                    <div class="text-sm text-blue-700">Hours/week - Extracurricular</div>
                </div>
                <div class="text-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-lg">
                    <div class="text-2xl font-bold text-green-600">{{ survey.work_hours }}</div>
                    <div class="text-sm text-green-700">Hours/week - Part-time Work</div>
                </div>
                <div class="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600">{{ survey.extracurricular_time|add:survey.work_hours }}</div>
                    <div class="text-sm text-purple-700">Total Commitment Hours</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Motivation Factors -->
    <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 mb-8">
        <h3 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
            <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mr-3">
                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                </svg>
            </div>
            Your Motivation Factors
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {% for factor in survey.motivation_factors %}
            <div class="flex items-center p-3 bg-gradient-to-r from-green-50 to-green-100 rounded-lg border border-green-200">
                <svg class="w-5 h-5 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="font-medium text-green-700">
                    {% if factor == 'career_advancement' %}Career Advancement
                    {% elif factor == 'personal_interest' %}Personal Interest
                    {% elif factor == 'family_expectations' %}Family Expectations
                    {% elif factor == 'financial_security' %}Financial Security
                    {% elif factor == 'intellectual_challenge' %}Intellectual Challenge
                    {% elif factor == 'helping_others' %}Helping Others
                    {% elif factor == 'creativity' %}Creativity and Innovation
                    {% elif factor == 'leadership' %}Leadership Opportunities
                    {% elif factor == 'work_life_balance' %}Work-Life Balance
                    {% elif factor == 'social_impact' %}Making a Social Impact
                    {% else %}{{ factor|title }}
                    {% endif %}
                </span>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Personalized Insights -->
    <div class="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl border border-indigo-200 p-6 mb-8">
        <h3 class="text-xl font-semibold text-indigo-800 mb-6 flex items-center">
            <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
            </svg>
            Personalized Insights
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {% for insight in insights %}
            <div class="bg-white rounded-lg p-4 border border-indigo-200">
                <div class="flex items-start">
                    <svg class="w-5 h-5 text-indigo-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                    <p class="text-indigo-700">{{ insight }}</p>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Additional Comments -->
    {% if survey.additional_comments %}
    <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 mb-8">
        <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <div class="w-8 h-8 bg-gradient-to-r from-gray-500 to-gray-600 rounded-full flex items-center justify-center mr-3">
                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                </svg>
            </div>
            Your Additional Comments
        </h3>
        <div class="bg-gray-50 rounded-lg p-4">
            <p class="text-gray-700 italic">"{{ survey.additional_comments }}"</p>
        </div>
    </div>
    {% endif %}

    <!-- Action Buttons -->
    <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a href="{% url 'student_dashboard' %}" 
           class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Dashboard
        </a>
        <a href="{% url 'student_survey' %}" 
           class="inline-flex items-center justify-center px-6 py-3 border border-primary-300 text-base font-medium rounded-lg text-primary-700 bg-primary-50 hover:bg-primary-100 transition-all duration-200">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            Update Survey
        </a>
        <a href="{% url 'recommendations' %}" 
           class="inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 transition-all duration-200 hover:shadow-lg transform hover:-translate-y-0.5">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
            </svg>
            View Recommendations
        </a>
    </div>
</div>

<style>
    .backdrop-blur-sm {
        backdrop-filter: blur(4px);
    }
</style>
{% endblock %}
