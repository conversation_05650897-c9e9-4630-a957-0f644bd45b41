{% extends 'base.html' %}

{% block title %}{{ action }} Student - {{ block.super }}{% endblock %}

{% block sidebar %}
    {% include 'admin/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ action }} Student</h1>
                    <p class="mt-2 text-gray-600">
                        {% if action == 'Create' %}
                            Add a new student to the system
                        {% else %}
                            Update student information
                        {% endif %}
                    </p>
                </div>
                <a href="{% url 'management_students' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors inline-flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"/>
                    </svg>
                    Back to Students
                </a>
            </div>
        </div>

        <!-- Form -->
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <!-- Personal Information -->
            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Personal Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                        <input type="text" id="first_name" name="first_name" 
                               value="{% if student %}{{ student.user.first_name }}{% endif %}"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                               required>
                    </div>
                    <div>
                        <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                        <input type="text" id="last_name" name="last_name" 
                               value="{% if student %}{{ student.user.last_name }}{% endif %}"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                               required>
                    </div>
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                        <input type="email" id="email" name="email" 
                               value="{% if student %}{{ student.user.email }}{% endif %}"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                               required>
                    </div>
                    {% if action == 'Create' %}
                    <div>
                        <label for="username" class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                        <input type="text" id="username" name="username" 
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                               required>
                    </div>
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                        <input type="password" id="password" name="password" 
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                               required>
                    </div>
                    {% endif %}
                    <div>
                        <label for="phone_number" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                        <input type="tel" id="phone_number" name="phone_number" 
                               value="{% if student %}{{ student.phone_number }}{% endif %}"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    </div>
                    <div>
                        <label for="date_of_birth" class="block text-sm font-medium text-gray-700 mb-2">Date of Birth</label>
                        <input type="date" id="date_of_birth" name="date_of_birth" 
                               value="{% if student %}{{ student.date_of_birth|date:'Y-m-d' }}{% endif %}"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    </div>
                </div>
                <div class="mt-4">
                    <label for="address" class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                    <textarea id="address" name="address" rows="3"
                              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">{% if student %}{{ student.address }}{% endif %}</textarea>
                </div>
            </div>

            <!-- Academic Information -->
            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Academic Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="student_id" class="block text-sm font-medium text-gray-700 mb-2">Student ID</label>
                        <input type="text" id="student_id" name="student_id" 
                               value="{% if student %}{{ student.student_id }}{% endif %}"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                               required>
                    </div>
                    <div>
                        <label for="year" class="block text-sm font-medium text-gray-700 mb-2">Academic Year</label>
                        <select id="year" name="year" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500" required>
                            <option value="">Select Year</option>
                            <option value="freshman" {% if student and student.year == 'freshman' %}selected{% endif %}>Freshman</option>
                            <option value="sophomore" {% if student and student.year == 'sophomore' %}selected{% endif %}>Sophomore</option>
                            <option value="junior" {% if student and student.year == 'junior' %}selected{% endif %}>Junior</option>
                            <option value="senior" {% if student and student.year == 'senior' %}selected{% endif %}>Senior</option>
                            <option value="graduate" {% if student and student.year == 'graduate' %}selected{% endif %}>Graduate</option>
                        </select>
                    </div>
                    <div>
                        <label for="major" class="block text-sm font-medium text-gray-700 mb-2">Major</label>
                        <select id="major" name="major" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <option value="">Select Major</option>
                            {% for department in departments %}
                            <option value="{{ department.id }}" {% if student and student.major == department %}selected{% endif %}>
                                {{ department.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div>
                        <label for="expected_graduation_year" class="block text-sm font-medium text-gray-700 mb-2">Expected Graduation Year</label>
                        <input type="number" id="expected_graduation_year" name="expected_graduation_year" 
                               value="{% if student %}{{ student.expected_graduation_year }}{% endif %}"
                               min="2024" max="2040"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    </div>
                    <div>
                        <label for="preferred_difficulty" class="block text-sm font-medium text-gray-700 mb-2">Preferred Difficulty</label>
                        <select id="preferred_difficulty" name="preferred_difficulty" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <option value="beginner" {% if student and student.preferred_difficulty == 'beginner' %}selected{% endif %}>Beginner</option>
                            <option value="intermediate" {% if student and student.preferred_difficulty == 'intermediate' %}selected{% endif %}>Intermediate</option>
                            <option value="advanced" {% if student and student.preferred_difficulty == 'advanced' %}selected{% endif %}>Advanced</option>
                        </select>
                    </div>
                </div>
                <div class="mt-4">
                    <label for="career_goals" class="block text-sm font-medium text-gray-700 mb-2">Career Goals</label>
                    <textarea id="career_goals" name="career_goals" rows="4"
                              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                              placeholder="Describe career aspirations and goals...">{% if student %}{{ student.career_goals }}{% endif %}</textarea>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{% url 'management_students' %}" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    Cancel
                </a>
                <button type="submit" class="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                    {{ action }} Student
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
