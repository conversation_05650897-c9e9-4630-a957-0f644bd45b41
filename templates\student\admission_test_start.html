{% extends 'student/base.html' %}

{% block title %}Admission Test - Course Recommendation System{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Admission Test</h1>
        <p class="text-lg text-gray-600">Complete this assessment to help us understand your academic strengths and provide better course recommendations.</p>
    </div>

    <!-- Test Information Card -->
    <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-8 mb-8">
        <div class="flex items-start space-x-4">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
            <div class="flex-1">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Test Overview</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg">
                        <div class="text-2xl font-bold text-blue-600">{{ questions_count }}</div>
                        <div class="text-sm text-blue-700">Questions</div>
                    </div>
                    <div class="text-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-lg">
                        <div class="text-2xl font-bold text-green-600">{{ estimated_time }}</div>
                        <div class="text-sm text-green-700">Minutes (Est.)</div>
                    </div>
                    <div class="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg">
                        <div class="text-2xl font-bold text-purple-600">5</div>
                        <div class="text-sm text-purple-700">Subject Areas</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Instructions -->
    <div class="bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-xl p-6 mb-8">
        <h3 class="text-lg font-semibold text-amber-800 mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
            Important Instructions
        </h3>
        <ul class="space-y-2 text-amber-700">
            <li class="flex items-start">
                <span class="flex-shrink-0 w-2 h-2 bg-amber-400 rounded-full mt-2 mr-3"></span>
                <span>Read each question carefully before selecting your answer</span>
            </li>
            <li class="flex items-start">
                <span class="flex-shrink-0 w-2 h-2 bg-amber-400 rounded-full mt-2 mr-3"></span>
                <span>You can only take this test once, so make sure you're ready</span>
            </li>
            <li class="flex items-start">
                <span class="flex-shrink-0 w-2 h-2 bg-amber-400 rounded-full mt-2 mr-3"></span>
                <span>The test covers Mathematics, Science, English, Logical Reasoning, and General Knowledge</span>
            </li>
            <li class="flex items-start">
                <span class="flex-shrink-0 w-2 h-2 bg-amber-400 rounded-full mt-2 mr-3"></span>
                <span>Your results will be used to provide personalized course recommendations</span>
            </li>
            <li class="flex items-start">
                <span class="flex-shrink-0 w-2 h-2 bg-amber-400 rounded-full mt-2 mr-3"></span>
                <span>Make sure you have a stable internet connection</span>
            </li>
        </ul>
    </div>

    <!-- Subject Areas Preview -->
    <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Test Subjects</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div class="flex items-center p-3 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg">
                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                    </svg>
                </div>
                <span class="font-medium text-blue-700">Mathematics</span>
            </div>
            <div class="flex items-center p-3 bg-gradient-to-r from-green-50 to-green-100 rounded-lg">
                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                    </svg>
                </div>
                <span class="font-medium text-green-700">Science</span>
            </div>
            <div class="flex items-center p-3 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg">
                <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <span class="font-medium text-purple-700">English</span>
            </div>
            <div class="flex items-center p-3 bg-gradient-to-r from-indigo-50 to-indigo-100 rounded-lg">
                <div class="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                    </svg>
                </div>
                <span class="font-medium text-indigo-700">Logical Reasoning</span>
            </div>
            <div class="flex items-center p-3 bg-gradient-to-r from-pink-50 to-pink-100 rounded-lg">
                <div class="w-8 h-8 bg-pink-500 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <span class="font-medium text-pink-700">General Knowledge</span>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a href="{% url 'student_dashboard' %}" 
           class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200 hover:shadow-md">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Dashboard
        </a>
        <a href="{% url 'admission_test_take' %}" 
           class="inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 transition-all duration-200 hover:shadow-lg transform hover:-translate-y-0.5">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
            </svg>
            Start Test
        </a>
    </div>
</div>

<style>
    .backdrop-blur-sm {
        backdrop-filter: blur(4px);
    }
</style>
{% endblock %}
