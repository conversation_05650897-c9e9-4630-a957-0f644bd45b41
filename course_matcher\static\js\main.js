// Custom JavaScript for the CourseRec application

console.log("main.js loaded");

// Three.js visualizations will be initialized here

// Enhanced sidebar functionality
document.addEventListener('DOMContentLoaded', function() {
    // Mobile sidebar functionality
    const sidebarToggle = document.querySelector('[data-sidebar-toggle]');
    const sidebar = document.querySelector('aside');
    
    // Close sidebar when clicking on a link (mobile)
    const sidebarLinks = document.querySelectorAll('.sidebar-nav-item');
    sidebarLinks.forEach(link => {
        link.addEventListener('click', function() {
            // Close sidebar on mobile after navigation
            if (window.innerWidth < 1024) {
                const sidebarOpen = document.querySelector('[x-data*="sidebarOpen"]');
                if (sidebarOpen && sidebarOpen.__x) {
                    sidebarOpen.__x.$data.sidebarOpen = false;
                }
            }
        });
    });
    
    // Close sidebar when clicking outside (mobile)
    document.addEventListener('click', function(e) {
        if (window.innerWidth < 1024) {
            const sidebarOpen = document.querySelector('[x-data*="sidebarOpen"]');
            if (sidebarOpen && sidebarOpen.__x && sidebarOpen.__x.$data.sidebarOpen) {
                if (!sidebar.contains(e.target) && !e.target.closest('[data-sidebar-toggle]')) {
                    sidebarOpen.__x.$data.sidebarOpen = false;
                }
            }
        }
    });
    
    // Add ripple effect to sidebar navigation items
    const sidebarItems = document.querySelectorAll('.sidebar-nav-item');
    
    sidebarItems.forEach(item => {
        item.addEventListener('click', function(e) {
            // Create ripple effect
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            this.appendChild(ripple);
            
            // Remove ripple after animation
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
    
    // Add hover sound effect (optional)
    sidebarItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            // Add subtle scale effect
            this.style.transform = 'scale(1.02)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
    
    // Add keyboard navigation support
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Tab') {
            // Add focus styles for accessibility
            const focusedElement = document.activeElement;
            if (focusedElement && focusedElement.classList.contains('sidebar-nav-item')) {
                focusedElement.style.outline = '2px solid #3b82f6';
                focusedElement.style.outlineOffset = '2px';
            }
        }
    });
    
    // Remove focus outline when clicking
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('sidebar-nav-item')) {
            e.target.style.outline = 'none';
        }
    });
    
    // Add loading states for navigation
    const handleNavigation = function(e) {
        const target = e.target.closest('.sidebar-nav-item');
        if (target) {
            // Add loading state
            target.style.opacity = '0.7';
            target.style.pointerEvents = 'none';
            
            // Remove loading state after navigation
            setTimeout(() => {
                target.style.opacity = '1';
                target.style.pointerEvents = 'auto';
            }, 1000);
        }
    };
    
    // Listen for HTMX events
    document.addEventListener('htmx:beforeRequest', handleNavigation);
    document.addEventListener('htmx:afterRequest', function() {
        // Re-enable all sidebar items
        sidebarItems.forEach(item => {
            item.style.opacity = '1';
            item.style.pointerEvents = 'auto';
        });
    });
});

// Add CSS for ripple effect
const style = document.createElement('style');
style.textContent = `
    .sidebar-nav-item {
        position: relative;
        overflow: hidden;
    }
    
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(59, 130, 246, 0.3);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .sidebar-nav-item:focus {
        outline: 2px solid #3b82f6;
        outline-offset: 2px;
    }
    
    .sidebar-nav-item:focus:not(:focus-visible) {
        outline: none;
    }
`;
document.head.appendChild(style);
