from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.models import User
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db import models
from django.views.decorators.http import require_http_methods
from .forms import StudentRegistrationForm, AcademicRecordForm, StudentInterestsForm, CareerGoalsForm, AdmissionTestForm, StudentSurveyForm
from .models import StudentProfile, Course, AcademicRecord, Recommendation, Department, AdmissionTest, AdmissionTestAttempt, AdmissionTestAnswer, StudentSurvey
from .recommendation_service import RecommendationEngine
import json
import csv
import io
from django.core.exceptions import ValidationError

def get_profile_completion_status(student):
    """
    Calculate profile completion status for a student.
    Returns a dictionary with completion status for each component.
    """
    return {
        'academic_records': AcademicRecord.objects.filter(student=student).exists(),
        'interests': bool(student.interests and len(student.interests) > 0),
        'career_goals': bool(student.career_goals and student.career_goals.strip()),
        'major': bool(student.major),
        'admission_test': AdmissionTestAttempt.objects.filter(student=student, is_completed=True).exists(),
        'survey': StudentSurvey.objects.filter(student=student).exists(),
    }

def student_registration(request):
    if request.method == 'POST':
        form = StudentRegistrationForm(request.POST)
        if form.is_valid():
            # Create User with all the required details
            user = User.objects.create_user(
                username=form.cleaned_data['username'],
                email=form.cleaned_data['email'],
                password=form.cleaned_data['password'],
                first_name=form.cleaned_data.get('first_name', ''),
                last_name=form.cleaned_data.get('last_name', '')
            )

            # Create StudentProfile from the form, linking it to the new user
            student_profile = form.save(commit=False)
            student_profile.user = user
            student_profile.save()

            # It's good practice to call save_m2m() when using commit=False, in case of M2M fields.
            form.save_m2m()

            return redirect('login')  # Redirect to login page after successful registration
    else:
        form = StudentRegistrationForm()
    return render(request, 'registration/register.html', {'form': form})

def redirect_after_login(request):
    if request.user.is_staff or request.user.is_superuser:
        return redirect('management_dashboard')
    else:
        return redirect('student_dashboard')

def landing_page(request):
    if request.user.is_authenticated:
        return redirect('redirect_after_login')
    return render(request, 'landing_page.html')

@login_required
def home(request):
    """Student dashboard with overview metrics and quick actions"""
    try:
        student = request.user.studentprofile
    except StudentProfile.DoesNotExist:
        # Redirect to complete profile if student profile doesn't exist
        messages.warning(request, "Please complete your student profile.")
        return redirect('student_registration')

    # Get dashboard data
    recent_records = AcademicRecord.objects.filter(student=student).order_by('-date_enrolled')[:5]
    recent_recommendations = Recommendation.objects.filter(student=student, is_dismissed=False).order_by('-confidence_score')[:3]

    # Calculate progress metrics
    total_courses = AcademicRecord.objects.filter(student=student).count()
    completed_courses = AcademicRecord.objects.filter(student=student, grade__isnull=False).count()

    # Calculate profile completion progress
    progress = get_profile_completion_status(student)

    # Calculate completion statistics
    progress['completed_count'] = sum(progress.values())
    progress['total_count'] = len(progress)
    progress['percentage'] = (progress['completed_count'] / progress['total_count']) * 100 if progress['total_count'] > 0 else 0
    progress['all_completed'] = progress['completed_count'] == progress['total_count']

    context = {
        'student': student,
        'recent_records': recent_records,
        'recent_recommendations': recent_recommendations,
        'total_courses': total_courses,
        'completed_courses': completed_courses,
        'interests_count': len(student.interests) if student.interests else 0,
        'has_career_goals': bool(student.career_goals),
        'progress': progress,
    }

    return render(request, 'student/dashboard.html', context)

@login_required
def student_academic_records(request):
    """Academic records management with CRUD operations"""
    try:
        student = request.user.studentprofile
    except StudentProfile.DoesNotExist:
        messages.error(request, "Student profile not found.")
        return redirect('student_dashboard')

    if request.method == 'POST':
        form = AcademicRecordForm(request.POST, student=student)
        if form.is_valid():
            record = form.save(commit=False)
            record.student = student
            record.save()
            messages.success(request, f"Academic record for {record.course.code} added successfully!")
            return redirect('academic_records')
    else:
        form = AcademicRecordForm(student=student)

    # Get existing records
    records = AcademicRecord.objects.filter(student=student).order_by('-year', '-semester', 'course__code')

    # Group records by semester for better display
    records_by_semester = {}
    for record in records:
        semester_key = f"{record.semester.title()} {record.year}"
        if semester_key not in records_by_semester:
            records_by_semester[semester_key] = []
        records_by_semester[semester_key].append(record)

    context = {
        'student': student,
        'form': form,
        'records': records,
        'records_by_semester': records_by_semester,
        'total_credits': student.total_credits,
        'gpa': student.gpa,
    }

    return render(request, 'student/academic_records.html', context)

@login_required
def student_interests(request):
    """Student interests management interface"""
    try:
        student = request.user.studentprofile
    except StudentProfile.DoesNotExist:
        messages.error(request, "Student profile not found.")
        return redirect('student_dashboard')

    if request.method == 'POST':
        form = StudentInterestsForm(request.POST, student=student)
        if form.is_valid():
            form.save()
            messages.success(request, "Your interests have been updated successfully!")
            return redirect('student_interests')
    else:
        form = StudentInterestsForm(student=student)

    # Get suggested interests based on student's major and existing courses
    suggested_interests = []
    if student.major:
        # Get topics from courses in the student's major
        major_courses = Course.objects.filter(department=student.major)
        all_topics = []
        for course in major_courses:
            if course.topics:
                all_topics.extend(course.topics)

        # Get most common topics that aren't already in student's interests
        from collections import Counter
        topic_counts = Counter(all_topics)
        current_interests = student.interests or []
        suggested_interests = [topic for topic, count in topic_counts.most_common(10)
                             if topic not in current_interests][:5]

    context = {
        'student': student,
        'form': form,
        'current_interests': student.interests or [],
        'suggested_interests': suggested_interests,
    }

    return render(request, 'student/interests.html', context)

@login_required
def career_goals(request):
    """Career goals and preferences management"""
    try:
        student = request.user.studentprofile
    except StudentProfile.DoesNotExist:
        messages.error(request, "Student profile not found.")
        return redirect('student_dashboard')

    if request.method == 'POST':
        form = CareerGoalsForm(request.POST, instance=student)
        if form.is_valid():
            form.save()
            messages.success(request, "Your career goals have been updated successfully!")
            return redirect('career_goals')
    else:
        form = CareerGoalsForm(instance=student)

    # Get career-related course suggestions based on goals
    career_suggestions = []
    if student.career_goals:
        # Simple keyword matching for career-related courses
        career_keywords = student.career_goals.lower().split()
        for course in Course.objects.all():
            course_text = f"{course.name} {course.description}".lower()
            if any(keyword in course_text for keyword in career_keywords if len(keyword) > 3):
                career_suggestions.append(course)
        career_suggestions = career_suggestions[:5]  # Limit to 5 suggestions

    context = {
        'student': student,
        'form': form,
        'career_suggestions': career_suggestions,
    }

    return render(request, 'student/career_goals.html', context)

@login_required
def profile_completion(request):
    """Profile completion overview page"""
    try:
        student = request.user.studentprofile
    except StudentProfile.DoesNotExist:
        messages.error(request, "Student profile not found.")
        return redirect('student_registration')

    # Calculate profile completion progress
    progress = get_profile_completion_status(student)

    # Calculate completion statistics
    progress['completed_count'] = sum(progress.values())
    progress['total_count'] = len(progress)
    progress['percentage'] = (progress['completed_count'] / progress['total_count']) * 100 if progress['total_count'] > 0 else 0
    progress['all_completed'] = progress['completed_count'] == progress['total_count']

    context = {
        'student': student,
        'progress': progress,
    }

    return render(request, 'student/profile_completion.html', context)

@login_required
def select_major(request):
    """Major selection interface"""
    try:
        student = request.user.studentprofile
    except StudentProfile.DoesNotExist:
        messages.error(request, "Student profile not found.")
        return redirect('student_registration')

    if request.method == 'POST':
        major_id = request.POST.get('major')
        if major_id:
            try:
                major = Department.objects.get(id=major_id)
                student.major = major
                student.save()
                messages.success(request, f"Your major has been set to {major.name}!")
                return redirect('profile_completion')
            except Department.DoesNotExist:
                messages.error(request, "Selected major not found.")
        else:
            messages.error(request, "Please select a major.")

    # Get all available departments
    departments = Department.objects.all().order_by('name')

    context = {
        'student': student,
        'departments': departments,
    }

    return render(request, 'student/select_major.html', context)

@login_required
def recommendations(request):
    """Course recommendations with personalized suggestions"""
    try:
        student = request.user.studentprofile
    except StudentProfile.DoesNotExist:
        messages.error(request, "Student profile not found.")
        return redirect('student_dashboard')

    # Check if all required components are completed
    profile_completion = get_profile_completion_status(student)

    all_completed = all(profile_completion.values())

    if not all_completed:
        # Show incomplete profile message instead of recommendations
        missing_components = [
            component for component, completed in profile_completion.items()
            if not completed
        ]

        context = {
            'student': student,
            'profile_incomplete': True,
            'missing_components': missing_components,
            'profile_completion': profile_completion,
        }
        return render(request, 'student/recommendations.html', context)

    # Generate recommendations using the recommendation engine
    engine = RecommendationEngine()

    # Get or create fresh recommendations
    if request.GET.get('refresh') == 'true':
        # Clear existing recommendations and generate new ones
        Recommendation.objects.filter(student=student).delete()

    existing_recommendations = Recommendation.objects.filter(
        student=student,
        is_dismissed=False
    ).order_by('-confidence_score')

    if not existing_recommendations.exists():
        # Generate new recommendations
        try:
            new_recommendations = engine.get_recommendations(student, limit=10)
            # Save recommendations to database
            for rec in new_recommendations:
                Recommendation.objects.create(
                    student=student,
                    course=rec.course,
                    confidence_score=rec.confidence_score,
                    recommendation_type=rec.recommendation_type,
                    reasoning=rec.reasoning
                )
            existing_recommendations = Recommendation.objects.filter(
                student=student,
                is_dismissed=False
            ).order_by('-confidence_score')
        except Exception as e:
            messages.warning(request, "Unable to generate recommendations at this time. Please try again later.")
            existing_recommendations = Recommendation.objects.none()  # Return empty QuerySet instead of list

    # Get only the top 3 recommendations ordered by confidence score
    top_recommendations = existing_recommendations[:3]

    context = {
        'student': student,
        'recommendations': top_recommendations,  # Only top 3 recommendations
        'total_recommendations': existing_recommendations.count(),
        'showing_top_3': True,  # Flag to indicate we're showing limited results
    }

    return render(request, 'student/recommendations.html', context)

# HTMX action views for dynamic interactions
@login_required
@require_http_methods(["POST"])
def bookmark_course(request, course_id):
    """Bookmark/unbookmark a course (placeholder for future functionality)"""
    try:
        course = get_object_or_404(Course, id=course_id)
        # This could be implemented with a Bookmark model in the future
        return JsonResponse({'status': 'success', 'message': f'Course {course.code} bookmarked!'})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=400)

@login_required
@require_http_methods(["DELETE"])
def delete_interest(request, interest_id):
    """Remove an interest from student's profile"""
    try:
        student = request.user.studentprofile
        interests = student.interests or []

        # Remove interest by index (interest_id is the index)
        if 0 <= interest_id < len(interests):
            removed_interest = interests.pop(interest_id)
            student.interests = interests
            student.save()
            return JsonResponse({'status': 'success', 'message': f'Removed "{removed_interest}"'})
        else:
            return JsonResponse({'status': 'error', 'message': 'Interest not found'}, status=404)
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=400)

@login_required
@require_http_methods(["POST"])
def add_interest(request):
    """Add a new interest to student's profile"""
    try:
        student = request.user.studentprofile
        new_interest = request.POST.get('interest', '').strip()

        if not new_interest:
            return JsonResponse({'status': 'error', 'message': 'Interest cannot be empty'}, status=400)

        interests = student.interests or []
        if new_interest not in interests:
            interests.append(new_interest)
            student.interests = interests
            student.save()
            return JsonResponse({'status': 'success', 'message': f'Added "{new_interest}"'})
        else:
            return JsonResponse({'status': 'error', 'message': 'Interest already exists'}, status=400)
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=400)

@login_required
@require_http_methods(["POST"])
def add_suggested_interest(request):
    """Add a suggested interest to student's profile"""
    try:
        student = request.user.studentprofile
        suggested_interest = request.POST.get('interest', '').strip()

        if not suggested_interest:
            return JsonResponse({'status': 'error', 'message': 'Interest cannot be empty'}, status=400)

        interests = student.interests or []
        if suggested_interest not in interests:
            interests.append(suggested_interest)
            student.interests = interests
            student.save()
            return JsonResponse({'status': 'success', 'message': f'Added "{suggested_interest}"'})
        else:
            return JsonResponse({'status': 'error', 'message': 'Interest already exists'}, status=400)
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=400)

@login_required
@require_http_methods(["DELETE"])
def delete_academic_record(request, record_id):
    """Delete an academic record"""
    try:
        student = request.user.studentprofile
        record = get_object_or_404(AcademicRecord, id=record_id, student=student)
        course_code = record.course.code
        record.delete()
        return JsonResponse({'status': 'success', 'message': f'Removed {course_code} from your records'})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=400)

@login_required
@require_http_methods(["POST"])
def add_academic_record(request):
    """Add a new academic record via HTMX"""
    try:
        student = request.user.studentprofile
        form = AcademicRecordForm(request.POST, student=student)

        if form.is_valid():
            record = form.save(commit=False)
            record.student = student
            record.save()
            return JsonResponse({
                'status': 'success',
                'message': f'Added {record.course.code} to your academic records'
            })
        else:
            errors = []
            for field, error_list in form.errors.items():
                for error in error_list:
                    errors.append(f"{field}: {error}")
            return JsonResponse({'status': 'error', 'message': '; '.join(errors)}, status=400)
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=400)

# Legacy placeholder views (keeping for compatibility)
def update_career_goal(request, goal_id):
    return HttpResponse(status=204)

def add_career_goal(request):
    return HttpResponse(status=204)

def delete_career_goal(request, goal_id):
    return HttpResponse(status=204)

# Admin Views
@login_required
def admin_dashboard(request):
    """Admin dashboard with real statistics and metrics"""
    # Get real statistics from the database
    total_students = StudentProfile.objects.count()
    total_courses = Course.objects.count()
    total_departments = Department.objects.count()

    # Get recent activity data
    recent_students = StudentProfile.objects.select_related('user', 'major').order_by('-user__date_joined')[:5]
    recent_courses = Course.objects.select_related('department').order_by('-id')[:5]

    # Calculate some metrics
    students_by_year = {}
    for choice in StudentProfile.YEAR_CHOICES:
        year_key = choice[0]
        year_label = choice[1]
        count = StudentProfile.objects.filter(year=year_key).count()
        students_by_year[year_label] = count

    # Get average GPA
    students_with_records = StudentProfile.objects.filter(academicrecord__isnull=False).distinct()
    total_gpa = sum(student.gpa for student in students_with_records)
    avg_gpa = total_gpa / len(students_with_records) if students_with_records else 0

    # Get course enrollment stats
    popular_courses = Course.objects.annotate(
        enrollment_count=models.Count('academicrecord')
    ).order_by('-enrollment_count')[:5]

    context = {
        'total_students': total_students,
        'total_courses': total_courses,
        'total_departments': total_departments,
        'avg_gpa': round(avg_gpa, 2),
        'recent_students': recent_students,
        'recent_courses': recent_courses,
        'students_by_year': students_by_year,
        'popular_courses': popular_courses,
    }

    return render(request, 'admin/dashboard.html', context)

@login_required
def admin_students(request):
    """Student management interface with search and filtering"""
    students = StudentProfile.objects.select_related('user', 'major').all()
    departments = Department.objects.all()

    context = {
        'students': students,
        'departments': departments,
    }
    return render(request, 'admin/students.html', context)

@login_required
def admin_courses(request):
    """Course management interface with search and filtering"""
    courses = Course.objects.select_related('department').all()
    departments = Department.objects.all()

    context = {
        'courses': courses,
        'departments': departments,
    }
    return render(request, 'admin/courses.html', context)

def admin_advising(request):
    return render(request, 'admin/advising.html')

def admin_reports(request):
    return render(request, 'admin/reports.html')

# HTMX API Views
@login_required
def recent_activity_api(request):
    """Get recent activity for the admin dashboard"""
    from django.utils import timezone
    from datetime import timedelta

    # Get recent activities from the last 7 days
    week_ago = timezone.now() - timedelta(days=7)

    activities = []

    # Recent student registrations
    recent_students = StudentProfile.objects.select_related('user').filter(
        user__date_joined__gte=week_ago
    ).order_by('-user__date_joined')[:5]

    for student in recent_students:
        activities.append({
            'type': 'student_registration',
            'message': f'Student <strong>{student.user.get_full_name() or student.user.username}</strong> registered',
            'time': student.user.date_joined,
            'icon': 'user-plus'
        })

    # Recent academic records
    recent_records = AcademicRecord.objects.select_related('student__user', 'course').filter(
        date_enrolled__gte=week_ago
    ).order_by('-date_enrolled')[:5]

    for record in recent_records:
        activities.append({
            'type': 'enrollment',
            'message': f'<strong>{record.student.user.get_full_name() or record.student.user.username}</strong> enrolled in {record.course.code}',
            'time': record.date_enrolled,
            'icon': 'book-open'
        })

    # Sort all activities by time
    activities.sort(key=lambda x: x['time'], reverse=True)
    activities = activities[:10]  # Limit to 10 most recent

    context = {'activities': activities}
    return render(request, 'admin/partials/_recent_activity.html', context)

def student_list_api(request):
    students = StudentProfile.objects.select_related('user', 'major').all()[:50] # Limiting for performance
    return render(request, 'admin/partials/_student_list.html', {'students': students})

def search_students_api(request):
    search_text = request.POST.get('search', '').strip()
    
    if search_text:
        # A more comprehensive search would query across multiple fields
        students = StudentProfile.objects.select_related('user', 'major').filter(
            models.Q(user__first_name__icontains=search_text) |
            models.Q(user__last_name__icontains=search_text) |
            models.Q(student_id__icontains=search_text) |
            models.Q(major__name__icontains=search_text)
        )[:50]
    else:
        students = StudentProfile.objects.select_related('user', 'major').all()[:50]
        
    return render(request, 'admin/partials/_student_list.html', {'students': students})

def course_list_api(request):
    courses = Course.objects.select_related('department').all()[:50]
    return render(request, 'admin/partials/_course_list.html', {'courses': courses})

def search_courses_api(request):
    search_text = request.POST.get('search', '').strip()
    
    if search_text:
        courses = Course.objects.select_related('department').filter(
            models.Q(name__icontains=search_text) |
            models.Q(code__icontains=search_text) |
            models.Q(department__name__icontains=search_text)
        )[:50]
    else:
        courses = Course.objects.select_related('department').all()[:50]
        
    return render(request, 'admin/partials/_course_list.html', {'courses': courses})


# Department/Major Management Views
@login_required
def admin_departments(request):
    """Department management with CRUD operations"""
    departments = Department.objects.all().order_by('name')

    context = {
        'departments': departments,
        'total_departments': departments.count(),
    }

    return render(request, 'admin/departments.html', context)

@login_required
def admin_department_create(request):
    """Create a new department"""
    if request.method == 'POST':
        name = request.POST.get('name', '').strip()
        code = request.POST.get('code', '').strip().upper()
        description = request.POST.get('description', '').strip()

        if not name or not code:
            messages.error(request, 'Department name and code are required.')
        elif Department.objects.filter(code=code).exists():
            messages.error(request, f'Department with code "{code}" already exists.')
        else:
            try:
                department = Department.objects.create(
                    name=name,
                    code=code,
                    description=description
                )
                messages.success(request, f'Department "{department.name}" created successfully!')
                return redirect('management_departments')
            except Exception as e:
                messages.error(request, f'Error creating department: {str(e)}')

    context = {
        'action': 'Create',
    }
    return render(request, 'admin/department_form.html', context)

@login_required
def admin_department_edit(request, department_id):
    """Edit an existing department"""
    department = get_object_or_404(Department, id=department_id)

    if request.method == 'POST':
        name = request.POST.get('name', '').strip()
        code = request.POST.get('code', '').strip().upper()
        description = request.POST.get('description', '').strip()

        if not name or not code:
            messages.error(request, 'Department name and code are required.')
        elif Department.objects.filter(code=code).exclude(id=department.id).exists():
            messages.error(request, f'Department with code "{code}" already exists.')
        else:
            try:
                department.name = name
                department.code = code
                department.description = description
                department.save()
                messages.success(request, f'Department "{department.name}" updated successfully!')
                return redirect('management_departments')
            except Exception as e:
                messages.error(request, f'Error updating department: {str(e)}')

    context = {
        'department': department,
        'action': 'Edit',
    }
    return render(request, 'admin/department_form.html', context)

@login_required
def admin_department_delete(request, department_id):
    """Delete a department"""
    department = get_object_or_404(Department, id=department_id)

    # Check if department has associated courses or students
    course_count = department.course_set.count()
    student_count = department.studentprofile_set.count()

    if course_count > 0 or student_count > 0:
        messages.error(request, f'Cannot delete department "{department.name}". It has {course_count} courses and {student_count} students associated with it.')
    else:
        department_name = department.name
        department.delete()
        messages.success(request, f'Department "{department_name}" deleted successfully!')

    return redirect('management_departments')


# Student CRUD Views
@login_required
def admin_student_create(request):
    """Create a new student"""
    if request.method == 'POST':
        form = StudentRegistrationForm(request.POST)
        if form.is_valid():
            # Create user first
            user = User.objects.create_user(
                username=form.cleaned_data['username'],
                email=form.cleaned_data['email'],
                first_name=form.cleaned_data['first_name'],
                last_name=form.cleaned_data['last_name'],
                password=form.cleaned_data['password']
            )

            # Create student profile
            student = form.save(commit=False)
            student.user = user
            student.save()

            messages.success(request, f'Student {user.get_full_name()} created successfully!')
            return redirect('management_students')
    else:
        form = StudentRegistrationForm()

    return render(request, 'admin/student_form.html', {'form': form, 'action': 'Create'})

@login_required
def admin_student_view(request, student_id):
    """View student details"""
    student = get_object_or_404(StudentProfile, id=student_id)
    academic_records = AcademicRecord.objects.filter(student=student).select_related('course')

    context = {
        'student': student,
        'academic_records': academic_records,
    }
    return render(request, 'admin/student_detail.html', context)

@login_required
def admin_student_edit(request, student_id):
    """Edit student information"""
    student = get_object_or_404(StudentProfile, id=student_id)

    if request.method == 'POST':
        # Create a form that handles both User and StudentProfile data
        user_data = {
            'first_name': request.POST.get('first_name'),
            'last_name': request.POST.get('last_name'),
            'email': request.POST.get('email'),
        }

        # Update user information
        for field, value in user_data.items():
            if value:
                setattr(student.user, field, value)
        student.user.save()

        # Update student profile
        student_fields = ['student_id', 'year', 'major', 'date_of_birth', 'phone_number',
                         'address', 'expected_graduation_year', 'career_goals', 'preferred_difficulty']

        for field in student_fields:
            value = request.POST.get(field)
            if value:
                if field == 'major':
                    try:
                        major = Department.objects.get(id=value)
                        student.major = major
                    except Department.DoesNotExist:
                        pass
                else:
                    setattr(student, field, value)

        student.save()
        messages.success(request, f'Student {student.user.get_full_name()} updated successfully!')
        return redirect('admin_student_view', student_id=student.id)

    departments = Department.objects.all()
    context = {
        'student': student,
        'departments': departments,
        'action': 'Edit'
    }
    return render(request, 'admin/student_form.html', context)

@login_required
def admin_student_delete(request, student_id):
    """Delete a student"""
    student = get_object_or_404(StudentProfile, id=student_id)

    if request.method == 'POST':
        student_name = student.user.get_full_name()
        student.user.delete()  # This will cascade delete the student profile
        messages.success(request, f'Student {student_name} deleted successfully!')
        return redirect('management_students')

    return render(request, 'admin/student_confirm_delete.html', {'student': student})


# Course CRUD Views
@login_required
def admin_course_create(request):
    """Create a new course"""
    if request.method == 'POST':
        course_data = {
            'name': request.POST.get('name'),
            'code': request.POST.get('code'),
            'department_id': request.POST.get('department'),
            'credits': request.POST.get('credits'),
            'description': request.POST.get('description'),
            'difficulty': request.POST.get('difficulty'),
        }

        try:
            department = Department.objects.get(id=course_data['department_id'])
            course = Course.objects.create(
                name=course_data['name'],
                code=course_data['code'],
                department=department,
                credits=int(course_data['credits']),
                description=course_data['description'],
                difficulty=course_data['difficulty']
            )

            messages.success(request, f'Course {course.code} - {course.name} created successfully!')
            return redirect('management_courses')
        except Exception as e:
            messages.error(request, f'Error creating course: {str(e)}')

    departments = Department.objects.all()
    context = {
        'departments': departments,
        'action': 'Create',
        'difficulty_choices': Course.DIFFICULTY_CHOICES,
    }
    return render(request, 'admin/course_form.html', context)

@login_required
def admin_course_view(request, course_id):
    """View course details"""
    course = get_object_or_404(Course, id=course_id)
    enrolled_students = AcademicRecord.objects.filter(course=course).select_related('student__user')

    context = {
        'course': course,
        'enrolled_students': enrolled_students,
    }
    return render(request, 'admin/course_detail.html', context)

@login_required
def admin_course_edit(request, course_id):
    """Edit course information"""
    course = get_object_or_404(Course, id=course_id)

    if request.method == 'POST':
        try:
            course.name = request.POST.get('name')
            course.code = request.POST.get('code')
            course.department = Department.objects.get(id=request.POST.get('department'))
            course.credits = int(request.POST.get('credits'))
            course.description = request.POST.get('description')
            course.difficulty = request.POST.get('difficulty')
            course.save()

            messages.success(request, f'Course {course.code} - {course.name} updated successfully!')
            return redirect('admin_course_view', course_id=course.id)
        except Exception as e:
            messages.error(request, f'Error updating course: {str(e)}')

    departments = Department.objects.all()
    context = {
        'course': course,
        'departments': departments,
        'action': 'Edit',
        'difficulty_choices': Course.DIFFICULTY_CHOICES,
    }
    return render(request, 'admin/course_form.html', context)

@login_required
def admin_course_delete(request, course_id):
    """Delete a course"""
    course = get_object_or_404(Course, id=course_id)

    if request.method == 'POST':
        course_name = f'{course.code} - {course.name}'
        course.delete()
        messages.success(request, f'Course {course_name} deleted successfully!')
        return redirect('management_courses')

    return render(request, 'admin/course_confirm_delete.html', {'course': course})


# Admission Test Admin Views
@login_required
def admin_admission_tests(request):
    """Admission test management interface with search and filtering"""
    tests = AdmissionTest.objects.all().order_by('subject_area', 'difficulty_level')

    # Get statistics
    total_tests = tests.count()
    active_tests = tests.filter(is_active=True).count()
    total_attempts = AdmissionTestAttempt.objects.count()
    completed_attempts = AdmissionTestAttempt.objects.filter(is_completed=True).count()

    context = {
        'tests': tests,
        'total_tests': total_tests,
        'active_tests': active_tests,
        'total_attempts': total_attempts,
        'completed_attempts': completed_attempts,
    }
    return render(request, 'admin/admission_tests.html', context)

@login_required
def admin_admission_test_create(request):
    """Create a new admission test question"""
    if request.method == 'POST':
        try:
            # Get form data
            question_text = request.POST.get('question_text')
            subject_area = request.POST.get('subject_area')
            difficulty_level = request.POST.get('difficulty_level')
            correct_answer = request.POST.get('correct_answer')
            explanation = request.POST.get('explanation', '')
            is_active = request.POST.get('is_active') == 'on'

            # Get answer options
            options = []
            for i in range(1, 5):  # Support up to 4 options
                option = request.POST.get(f'option_{i}')
                if option:
                    options.append(option.strip())

            if len(options) < 2:
                messages.error(request, 'Please provide at least 2 answer options.')
                raise ValueError("Insufficient options")

            # Create the test question
            test = AdmissionTest.objects.create(
                question_text=question_text,
                subject_area=subject_area,
                difficulty_level=difficulty_level,
                answer_options=options,
                correct_answer=correct_answer,
                explanation=explanation,
                is_active=is_active
            )

            messages.success(request, f'Admission test question created successfully!')
            return redirect('management_admission_tests')

        except Exception as e:
            messages.error(request, f'Error creating admission test: {str(e)}')

    context = {
        'action': 'Create',
        'subject_choices': AdmissionTest.SUBJECT_CHOICES,
        'difficulty_choices': AdmissionTest.DIFFICULTY_CHOICES,
    }
    return render(request, 'admin/admission_test_form.html', context)

@login_required
def admin_admission_test_view(request, test_id):
    """View admission test details and statistics"""
    test = get_object_or_404(AdmissionTest, id=test_id)

    # Get statistics for this test
    attempts = AdmissionTestAnswer.objects.filter(question=test)
    total_attempts = attempts.count()
    correct_attempts = attempts.filter(student_answer=test.correct_answer).count()
    accuracy_rate = (correct_attempts / total_attempts * 100) if total_attempts > 0 else 0

    # Get recent attempts
    recent_attempts = AdmissionTestAnswer.objects.filter(question=test).select_related(
        'attempt__student__user'
    ).order_by('-attempt__date_taken')[:10]

    context = {
        'test': test,
        'total_attempts': total_attempts,
        'correct_attempts': correct_attempts,
        'accuracy_rate': round(accuracy_rate, 1),
        'recent_attempts': recent_attempts,
    }
    return render(request, 'admin/admission_test_detail.html', context)

@login_required
def admin_admission_test_edit(request, test_id):
    """Edit admission test question"""
    test = get_object_or_404(AdmissionTest, id=test_id)

    if request.method == 'POST':
        try:
            # Update test data
            test.question_text = request.POST.get('question_text')
            test.subject_area = request.POST.get('subject_area')
            test.difficulty_level = request.POST.get('difficulty_level')
            test.correct_answer = request.POST.get('correct_answer')
            test.explanation = request.POST.get('explanation', '')
            test.is_active = request.POST.get('is_active') == 'on'

            # Update answer options
            options = []
            for i in range(1, 5):  # Support up to 4 options
                option = request.POST.get(f'option_{i}')
                if option:
                    options.append(option.strip())

            if len(options) < 2:
                messages.error(request, 'Please provide at least 2 answer options.')
                raise ValueError("Insufficient options")

            test.answer_options = options
            test.save()

            messages.success(request, f'Admission test question updated successfully!')
            return redirect('admin_admission_test_view', test_id=test.id)

        except Exception as e:
            messages.error(request, f'Error updating admission test: {str(e)}')

    context = {
        'test': test,
        'action': 'Edit',
        'subject_choices': AdmissionTest.SUBJECT_CHOICES,
        'difficulty_choices': AdmissionTest.DIFFICULTY_CHOICES,
    }
    return render(request, 'admin/admission_test_form.html', context)

@login_required
def admin_admission_test_delete(request, test_id):
    """Delete an admission test question"""
    test = get_object_or_404(AdmissionTest, id=test_id)

    if request.method == 'POST':
        test_info = f'{test.subject_area} - {test.difficulty_level}'
        test.delete()
        messages.success(request, f'Admission test question ({test_info}) deleted successfully!')
        return redirect('management_admission_tests')

    # Get usage statistics
    attempts_count = AdmissionTestAnswer.objects.filter(question=test).count()

    context = {
        'test': test,
        'attempts_count': attempts_count,
    }
    return render(request, 'admin/admission_test_confirm_delete.html', context)

@login_required
def admin_admission_test_toggle_status(request, test_id):
    """Toggle active status of an admission test"""
    test = get_object_or_404(AdmissionTest, id=test_id)

    if request.method == 'POST':
        test.is_active = not test.is_active
        test.save()

        status = "activated" if test.is_active else "deactivated"
        messages.success(request, f'Admission test question {status} successfully!')

    return redirect('management_admission_tests')

@login_required
def admin_admission_test_statistics(request):
    """View comprehensive admission test statistics"""
    # Overall statistics
    total_tests = AdmissionTest.objects.count()
    active_tests = AdmissionTest.objects.filter(is_active=True).count()
    total_attempts = AdmissionTestAttempt.objects.count()
    completed_attempts = AdmissionTestAttempt.objects.filter(is_completed=True).count()

    # Subject area statistics
    subject_stats = {}
    for subject_code, subject_name in AdmissionTest.SUBJECT_CHOICES:
        tests_count = AdmissionTest.objects.filter(subject_area=subject_code).count()
        attempts_count = AdmissionTestAnswer.objects.filter(question__subject_area=subject_code).count()
        subject_stats[subject_name] = {
            'tests_count': tests_count,
            'attempts_count': attempts_count,
        }

    # Difficulty level statistics
    difficulty_stats = {}
    for difficulty_code, difficulty_name in AdmissionTest.DIFFICULTY_CHOICES:
        tests_count = AdmissionTest.objects.filter(difficulty_level=difficulty_code).count()
        attempts_count = AdmissionTestAnswer.objects.filter(question__difficulty_level=difficulty_code).count()
        difficulty_stats[difficulty_name] = {
            'tests_count': tests_count,
            'attempts_count': attempts_count,
        }

    # Recent activity
    recent_attempts = AdmissionTestAttempt.objects.filter(
        is_completed=True
    ).select_related('student__user').order_by('-date_taken')[:10]

    context = {
        'total_tests': total_tests,
        'active_tests': active_tests,
        'total_attempts': total_attempts,
        'completed_attempts': completed_attempts,
        'subject_stats': subject_stats,
        'difficulty_stats': difficulty_stats,
        'recent_attempts': recent_attempts,
    }
    return render(request, 'admin/admission_test_statistics.html', context)


# Admission Test Views
@login_required
def admission_test_start(request):
    """Start or continue admission test"""
    try:
        student = request.user.studentprofile
    except StudentProfile.DoesNotExist:
        messages.error(request, "Please complete your student profile first.")
        return redirect('student_registration')

    # Check if student already completed the test
    completed_attempt = AdmissionTestAttempt.objects.filter(
        student=student, is_completed=True
    ).first()

    if completed_attempt:
        messages.info(request, "You have already completed the admission test.")
        return redirect('admission_test_results')

    # Check for existing incomplete attempt
    incomplete_attempt = AdmissionTestAttempt.objects.filter(
        student=student, is_completed=False
    ).first()

    if incomplete_attempt:
        return redirect('admission_test_continue', attempt_id=incomplete_attempt.id)

    # Get active test questions
    questions = AdmissionTest.objects.filter(is_active=True).order_by('subject_area', 'difficulty_level')

    if not questions.exists():
        messages.error(request, "No admission test questions are currently available.")
        return redirect('student_dashboard')

    context = {
        'questions_count': questions.count(),
        'estimated_time': questions.count() * 2,  # 2 minutes per question
    }

    return render(request, 'student/admission_test_start.html', context)


@login_required
def admission_test_take(request):
    """Take the admission test"""
    try:
        student = request.user.studentprofile
    except StudentProfile.DoesNotExist:
        messages.error(request, "Please complete your student profile first.")
        return redirect('student_registration')

    # Check if already completed
    if AdmissionTestAttempt.objects.filter(student=student, is_completed=True).exists():
        messages.info(request, "You have already completed the admission test.")
        return redirect('admission_test_results')

    # Get or create attempt
    attempt, created = AdmissionTestAttempt.objects.get_or_create(
        student=student,
        is_completed=False,
        defaults={'max_possible_score': 0}
    )

    # Get test questions
    questions = AdmissionTest.objects.filter(is_active=True).order_by('subject_area', 'difficulty_level')

    if request.method == 'POST':
        form = AdmissionTestForm(questions, request.POST)
        if form.is_valid():
            answers = form.get_answers()

            # Save answers
            for question_id, answer in answers.items():
                question = AdmissionTest.objects.get(id=question_id)
                AdmissionTestAnswer.objects.update_or_create(
                    attempt=attempt,
                    question=question,
                    defaults={'student_answer': answer}
                )

            # Mark attempt as completed and calculate score
            from django.utils import timezone
            attempt.completed_at = timezone.now()
            attempt.is_completed = True

            # Calculate time taken
            time_diff = attempt.completed_at - attempt.started_at
            attempt.time_taken_minutes = int(time_diff.total_seconds() / 60)

            # Calculate score
            attempt.calculate_score()

            messages.success(request, f"Admission test completed! Your score: {attempt.percentage_score:.1f}%")
            return redirect('admission_test_results')
    else:
        form = AdmissionTestForm(questions)

    # Update max possible score
    if attempt.max_possible_score == 0:
        attempt.max_possible_score = sum(q.points for q in questions)
        attempt.save()

    context = {
        'form': form,
        'questions': questions,
        'attempt': attempt,
        'total_questions': questions.count(),
    }

    return render(request, 'student/admission_test_take.html', context)


@login_required
def admission_test_results(request):
    """View admission test results"""
    try:
        student = request.user.studentprofile
    except StudentProfile.DoesNotExist:
        messages.error(request, "Please complete your student profile first.")
        return redirect('student_registration')

    attempt = AdmissionTestAttempt.objects.filter(
        student=student, is_completed=True
    ).first()

    if not attempt:
        messages.warning(request, "You haven't completed the admission test yet.")
        return redirect('admission_test_start')

    # Get detailed results by subject
    answers = AdmissionTestAnswer.objects.filter(attempt=attempt).select_related('question')

    subject_results = {}
    for answer in answers:
        subject = answer.question.subject_area
        if subject not in subject_results:
            subject_results[subject] = {
                'total_questions': 0,
                'correct_answers': 0,
                'total_points': 0,
                'earned_points': 0,
            }

        subject_results[subject]['total_questions'] += 1
        subject_results[subject]['total_points'] += answer.question.points
        subject_results[subject]['earned_points'] += answer.points_earned
        if answer.is_correct:
            subject_results[subject]['correct_answers'] += 1

    # Calculate percentages
    for subject_data in subject_results.values():
        subject_data['percentage'] = (
            subject_data['earned_points'] / subject_data['total_points'] * 100
            if subject_data['total_points'] > 0 else 0
        )

    context = {
        'attempt': attempt,
        'subject_results': subject_results,
        'answers': answers,
    }

    return render(request, 'student/admission_test_results.html', context)


# Student Survey Views
@login_required
def student_survey(request):
    """Student learning preferences survey"""
    try:
        student = request.user.studentprofile
    except StudentProfile.DoesNotExist:
        messages.error(request, "Please complete your student profile first.")
        return redirect('student_registration')

    # Check if survey already completed
    try:
        existing_survey = StudentSurvey.objects.get(student=student)
        if request.method == 'GET':
            messages.info(request, "You have already completed the survey. You can update your responses below.")
    except StudentSurvey.DoesNotExist:
        existing_survey = None

    if request.method == 'POST':
        form = StudentSurveyForm(request.POST, instance=existing_survey)
        if form.is_valid():
            survey = form.save(commit=False)
            survey.student = student
            survey.save()

            if existing_survey:
                messages.success(request, "Your survey responses have been updated successfully!")
            else:
                messages.success(request, "Survey completed successfully! This will help us provide better course recommendations.")

            return redirect('student_dashboard')
    else:
        form = StudentSurveyForm(instance=existing_survey)

    context = {
        'form': form,
        'existing_survey': existing_survey,
    }

    return render(request, 'student/survey.html', context)


@login_required
def survey_results(request):
    """View survey results and insights"""
    try:
        student = request.user.studentprofile
    except StudentProfile.DoesNotExist:
        messages.error(request, "Please complete your student profile first.")
        return redirect('student_registration')

    try:
        survey = StudentSurvey.objects.get(student=student)
    except StudentSurvey.DoesNotExist:
        messages.warning(request, "Please complete the student survey first.")
        return redirect('student_survey')

    # Generate insights based on survey responses
    insights = []

    # Learning style insights
    if survey.learning_style == 'visual':
        insights.append("As a visual learner, you may benefit from courses with diagrams, charts, and visual presentations.")
    elif survey.learning_style == 'auditory':
        insights.append("As an auditory learner, discussion-based courses and lectures may be ideal for you.")
    elif survey.learning_style == 'kinesthetic':
        insights.append("As a kinesthetic learner, hands-on lab courses and practical applications may suit you best.")

    # Study preference insights
    if survey.study_preference == 'group':
        insights.append("Since you prefer group study, consider courses with team projects and collaborative assignments.")
    elif survey.study_preference == 'individual':
        insights.append("Your preference for individual study suggests you may excel in research-oriented courses.")

    # Workload insights
    total_commitments = survey.extracurricular_time + survey.work_hours
    if total_commitments > 20:
        insights.append("With your current commitments, consider a lighter course load or online/hybrid formats.")
    elif total_commitments < 10:
        insights.append("You have good availability for coursework and could handle a full course load.")

    # Technology comfort insights
    if survey.technology_comfort >= 8:
        insights.append("Your high comfort with technology makes you well-suited for online and tech-heavy courses.")
    elif survey.technology_comfort <= 4:
        insights.append("Consider traditional lecture-based courses or seek additional tech support resources.")

    context = {
        'survey': survey,
        'insights': insights,
    }

    return render(request, 'student/survey_results.html', context)


# Admission Test Import Views
@login_required
def admin_admission_test_import(request):
    """Bulk import admission test questions from CSV file"""
    if request.method == 'POST':
        if 'preview' in request.POST:
            # Handle file upload and preview
            return handle_import_preview(request)
        elif 'confirm_import' in request.POST:
            # Handle confirmed import
            return handle_import_confirmation(request)

    context = {
        'max_questions': 30,
        'subject_choices': AdmissionTest.SUBJECT_CHOICES,
        'difficulty_choices': AdmissionTest.DIFFICULTY_CHOICES,
    }
    return render(request, 'admin/admission_test_import.html', context)


@login_required
def admin_admission_test_template(request):
    """Download CSV template for admission test import"""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="admission_test_template.csv"'

    writer = csv.writer(response)

    # Write header
    writer.writerow([
        'question_text',
        'subject_area',
        'difficulty_level',
        'answer_options',
        'correct_answer',
        'explanation',
        'points'
    ])

    # Write sample data
    sample_questions = [
        [
            'What is the result of 2 + 2?',
            'math',
            'easy',
            '["2", "3", "4", "5"]',
            '4',
            'Basic addition: 2 + 2 = 4',
            '1'
        ],
        [
            'Which planet is closest to the Sun?',
            'science',
            'medium',
            '["Mercury", "Venus", "Earth", "Mars"]',
            'Mercury',
            'Mercury is the innermost planet in our solar system',
            '2'
        ],
        [
            'Who wrote "Romeo and Juliet"?',
            'english',
            'medium',
            '["William Shakespeare", "Charles Dickens", "Jane Austen", "Mark Twain"]',
            'William Shakespeare',
            'Romeo and Juliet is one of Shakespeare\'s most famous plays',
            '2'
        ]
    ]

    for question in sample_questions:
        writer.writerow(question)

    return response


def handle_import_preview(request):
    """Handle file upload and show preview of questions to be imported"""
    try:
        csv_file = request.FILES.get('csv_file')
        if not csv_file:
            messages.error(request, 'Please select a CSV file to upload.')
            return redirect('admin_admission_test_import')

        if not csv_file.name.endswith('.csv'):
            messages.error(request, 'Please upload a CSV file.')
            return redirect('admin_admission_test_import')

        # Read and parse CSV
        file_data = csv_file.read().decode('utf-8')
        csv_data = csv.DictReader(io.StringIO(file_data))

        questions_data = []
        errors = []
        line_number = 1  # Start from 1 (header is line 0)
        total_rows = 0

        # Count total rows first
        for _ in csv.DictReader(io.StringIO(file_data)):
            total_rows += 1

        # Process rows
        csv_data = csv.DictReader(io.StringIO(file_data))
        for row in csv_data:
            line_number += 1
            question_data, row_errors = validate_csv_row(row, line_number)

            if row_errors:
                errors.extend(row_errors)
            else:
                questions_data.append(question_data)

            # Enforce 30 question limit
            if len(questions_data) >= 30:
                if total_rows > 30:
                    errors.append(f"Maximum 30 questions allowed. Additional questions from line {line_number + 1} onwards will be ignored.")
                break

        if errors:
            for error in errors:
                messages.error(request, error)
            return redirect('admin_admission_test_import')

        if not questions_data:
            messages.error(request, 'No valid questions found in the CSV file.')
            return redirect('admin_admission_test_import')

        # Store data in session for confirmation step
        request.session['import_questions_data'] = questions_data

        context = {
            'questions_data': questions_data,
            'total_questions': len(questions_data),
            'subject_choices': dict(AdmissionTest.SUBJECT_CHOICES),
            'difficulty_choices': dict(AdmissionTest.DIFFICULTY_CHOICES),
        }

        return render(request, 'admin/admission_test_import_preview.html', context)

    except Exception as e:
        messages.error(request, f'Error processing CSV file: {str(e)}')
        return redirect('admin_admission_test_import')


def handle_import_confirmation(request):
    """Handle confirmed import of questions"""
    try:
        questions_data = request.session.get('import_questions_data', [])
        if not questions_data:
            messages.error(request, 'No import data found. Please upload the file again.')
            return redirect('admin_admission_test_import')

        # Get selected questions to import
        selected_indices = request.POST.getlist('selected_questions')
        if not selected_indices:
            messages.error(request, 'Please select at least one question to import.')
            return redirect('admin_admission_test_import')

        imported_count = 0
        errors = []

        for index_str in selected_indices:
            try:
                index = int(index_str)
                if 0 <= index < len(questions_data):
                    question_data = questions_data[index]

                    # Create the admission test question
                    AdmissionTest.objects.create(
                        question_text=question_data['question_text'],
                        subject_area=question_data['subject_area'],
                        difficulty_level=question_data['difficulty_level'],
                        options=question_data['answer_options'],
                        correct_answer=question_data['correct_answer'],
                        explanation=question_data['explanation'],
                        points=question_data['points'],
                        is_active=True
                    )
                    imported_count += 1

            except Exception as e:
                errors.append(f'Error importing question {index + 1}: {str(e)}')

        # Clear session data
        if 'import_questions_data' in request.session:
            del request.session['import_questions_data']

        # Show results
        if imported_count > 0:
            messages.success(request, f'Successfully imported {imported_count} question(s).')

        if errors:
            for error in errors:
                messages.error(request, error)

        return redirect('management_admission_tests')

    except Exception as e:
        messages.error(request, f'Error during import: {str(e)}')
        return redirect('admin_admission_test_import')


def validate_csv_row(row, line_number):
    """Validate a single CSV row and return processed data and errors"""
    errors = []
    question_data = {}

    # Required fields
    required_fields = ['question_text', 'subject_area', 'difficulty_level', 'answer_options', 'correct_answer']

    for field in required_fields:
        if not row.get(field, '').strip():
            errors.append(f'Line {line_number}: Missing required field "{field}"')
            return None, errors

    # Validate question_text
    question_text = row['question_text'].strip()
    if len(question_text) < 10:
        errors.append(f'Line {line_number}: Question text must be at least 10 characters long')
    else:
        question_data['question_text'] = question_text

    # Validate subject_area
    subject_area = row['subject_area'].strip().lower()
    valid_subjects = [choice[0] for choice in AdmissionTest.SUBJECT_CHOICES]
    if subject_area not in valid_subjects:
        errors.append(f'Line {line_number}: Invalid subject area "{subject_area}". Must be one of: {", ".join(valid_subjects)}')
    else:
        question_data['subject_area'] = subject_area

    # Validate difficulty_level
    difficulty_level = row['difficulty_level'].strip().lower()
    valid_difficulties = [choice[0] for choice in AdmissionTest.DIFFICULTY_CHOICES]
    if difficulty_level not in valid_difficulties:
        errors.append(f'Line {line_number}: Invalid difficulty level "{difficulty_level}". Must be one of: {", ".join(valid_difficulties)}')
    else:
        question_data['difficulty_level'] = difficulty_level

    # Validate and parse answer_options
    try:
        answer_options_str = row['answer_options'].strip()
        if answer_options_str.startswith('[') and answer_options_str.endswith(']'):
            answer_options = json.loads(answer_options_str)
        else:
            # Try to parse as comma-separated values
            answer_options = [opt.strip().strip('"\'') for opt in answer_options_str.split(',')]

        if not isinstance(answer_options, list) or len(answer_options) < 2 or len(answer_options) > 4:
            errors.append(f'Line {line_number}: Answer options must be a list of 2-4 options')
        else:
            question_data['answer_options'] = answer_options
    except json.JSONDecodeError:
        errors.append(f'Line {line_number}: Invalid format for answer_options. Use JSON array format like ["option1", "option2"]')

    # Validate correct_answer
    correct_answer = row['correct_answer'].strip()
    if 'answer_options' in question_data and correct_answer not in question_data['answer_options']:
        errors.append(f'Line {line_number}: Correct answer "{correct_answer}" must be one of the provided options')
    else:
        question_data['correct_answer'] = correct_answer

    # Validate explanation (optional)
    explanation = row.get('explanation', '').strip()
    question_data['explanation'] = explanation

    # Validate points (optional, default to 1)
    points_str = row.get('points', '1').strip()
    try:
        points = int(points_str)
        if points < 1 or points > 10:
            errors.append(f'Line {line_number}: Points must be between 1 and 10')
        else:
            question_data['points'] = points
    except ValueError:
        errors.append(f'Line {line_number}: Points must be a valid integer')

    if errors:
        return None, errors

    return question_data, []
