# Generated by Django 5.2.3 on 2025-07-06 08:44

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=10, unique=True)),
                ('description', models.TextField(blank=True)),
            ],
        ),
        migrations.CreateModel(
            name='Course',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=200)),
                ('code', models.Char<PERSON>ield(max_length=20, unique=True)),
                ('credits', models.IntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(6)])),
                ('description', models.TextField()),
                ('difficulty', models.CharField(choices=[('beginner', 'Beginner'), ('intermediate', 'Intermediate'), ('advanced', 'Advanced')], default='intermediate', max_length=20)),
                ('topics', models.JSONField(default=list, help_text='List of course topics/keywords for content-based matching')),
                ('prerequisites', models.ManyToManyField(blank=True, to='course_matcher.course')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='course_matcher.department')),
            ],
        ),
        migrations.CreateModel(
            name='StudentProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('student_id', models.CharField(max_length=20, unique=True)),
                ('year', models.CharField(choices=[('freshman', 'Freshman'), ('sophomore', 'Sophomore'), ('junior', 'Junior'), ('senior', 'Senior'), ('graduate', 'Graduate')], max_length=20)),
                ('interests', models.JSONField(default=list, help_text='List of academic interests/topics')),
                ('career_goals', models.TextField(blank=True)),
                ('preferred_difficulty', models.CharField(choices=[('beginner', 'Beginner'), ('intermediate', 'Intermediate'), ('advanced', 'Advanced')], default='intermediate', max_length=20)),
                ('date_of_birth', models.DateField(blank=True, null=True)),
                ('phone_number', models.CharField(blank=True, max_length=20)),
                ('address', models.TextField(blank=True)),
                ('expected_graduation_year', models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(2020), django.core.validators.MaxValueValidator(2040)])),
                ('major', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='course_matcher.department')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='AdvisingSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateTimeField()),
                ('status', models.CharField(choices=[('scheduled', 'Scheduled'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='scheduled', max_length=20)),
                ('notes', models.TextField(blank=True)),
                ('advisor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='advising_sessions', to=settings.AUTH_USER_MODEL)),
                ('course', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='course_matcher.course')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='course_matcher.studentprofile')),
            ],
        ),
        migrations.CreateModel(
            name='Recommendation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('confidence_score', models.FloatField(validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)])),
                ('recommendation_type', models.CharField(choices=[('classification', 'Classification-based'), ('knowledge', 'Knowledge-based'), ('content', 'Content-based'), ('hybrid', 'Hybrid')], max_length=20)),
                ('reasoning', models.TextField(help_text='Explanation for why this course was recommended')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_dismissed', models.BooleanField(default=False)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='course_matcher.course')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='course_matcher.studentprofile')),
            ],
            options={
                'ordering': ['-confidence_score', '-created_at'],
                'unique_together': {('student', 'course')},
            },
        ),
        migrations.CreateModel(
            name='AcademicRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('semester', models.CharField(choices=[('fall', 'Fall'), ('spring', 'Spring'), ('summer', 'Summer')], max_length=20)),
                ('year', models.IntegerField()),
                ('grade', models.CharField(blank=True, choices=[('A', 'A'), ('A-', 'A-'), ('B+', 'B+'), ('B', 'B'), ('B-', 'B-'), ('C+', 'C+'), ('C', 'C'), ('C-', 'C-'), ('D+', 'D+'), ('D', 'D'), ('F', 'F')], max_length=2, null=True)),
                ('date_enrolled', models.DateTimeField(auto_now_add=True)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='course_matcher.course')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='course_matcher.studentprofile')),
            ],
            options={
                'unique_together': {('student', 'course')},
            },
        ),
    ]
