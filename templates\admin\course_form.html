{% extends 'base.html' %}

{% block title %}{{ action }} Course - {{ block.super }}{% endblock %}

{% block sidebar %}
    {% include 'admin/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ action }} Course</h1>
                    <p class="mt-2 text-gray-600">
                        {% if action == 'Create' %}
                            Add a new course to the catalog
                        {% else %}
                            Update course information
                        {% endif %}
                    </p>
                </div>
                <a href="{% url 'management_courses' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors inline-flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"/>
                    </svg>
                    Back to Courses
                </a>
            </div>
        </div>

        <!-- Form -->
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <!-- Basic Information -->
            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Basic Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Course Name</label>
                        <input type="text" id="name" name="name" 
                               value="{% if course %}{{ course.name }}{% endif %}"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                               placeholder="e.g., Introduction to Computer Science"
                               required>
                    </div>
                    <div>
                        <label for="code" class="block text-sm font-medium text-gray-700 mb-2">Course Code</label>
                        <input type="text" id="code" name="code" 
                               value="{% if course %}{{ course.code }}{% endif %}"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                               placeholder="e.g., CS101"
                               required>
                    </div>
                    <div>
                        <label for="department" class="block text-sm font-medium text-gray-700 mb-2">Department</label>
                        <select id="department" name="department" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500" required>
                            <option value="">Select Department</option>
                            {% for department in departments %}
                            <option value="{{ department.id }}" {% if course and course.department == department %}selected{% endif %}>
                                {{ department.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div>
                        <label for="credits" class="block text-sm font-medium text-gray-700 mb-2">Credits</label>
                        <input type="number" id="credits" name="credits" 
                               value="{% if course %}{{ course.credits }}{% endif %}"
                               min="1" max="6"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                               required>
                    </div>
                    <div>
                        <label for="difficulty" class="block text-sm font-medium text-gray-700 mb-2">Difficulty Level</label>
                        <select id="difficulty" name="difficulty" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500" required>
                            {% for value, label in difficulty_choices %}
                            <option value="{{ value }}" {% if course and course.difficulty == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>

            <!-- Course Description -->
            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Course Description</h3>
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea id="description" name="description" rows="6"
                              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                              placeholder="Provide a detailed description of the course content, objectives, and learning outcomes..."
                              required>{% if course %}{{ course.description }}{% endif %}</textarea>
                </div>
            </div>

            <!-- Prerequisites (Future Enhancement) -->
            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Prerequisites</h3>
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-sm font-medium text-blue-800">Prerequisites Management</h4>
                            <p class="mt-1 text-sm text-blue-700">
                                Prerequisites management will be available in a future update. For now, prerequisites can be managed through the course relationships.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{% url 'management_courses' %}" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    Cancel
                </a>
                <button type="submit" class="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                    {{ action }} Course
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
