from django.urls import path
from . import views

urlpatterns = [
    path('redirect-after-login/', views.redirect_after_login, name='redirect_after_login'),
    path('register/', views.student_registration, name='student_registration'),
    # Student-facing URL (can be expanded later)
    path('', views.landing_page, name='landing_page'),
    path('student/dashboard/', views.home, name='student_dashboard'),
    path('academic-records/', views.student_academic_records, name='academic_records'),
    path('interests/', views.student_interests, name='student_interests'),
    path('career-goals/', views.career_goals, name='career_goals'),
    path('profile-completion/', views.profile_completion, name='profile_completion'),
    path('select-major/', views.select_major, name='select_major'),
    path('recommendations/', views.recommendations, name='recommendations'),

    # Admission Test URLs
    path('admission-test/', views.admission_test_start, name='admission_test_start'),
    path('admission-test/take/', views.admission_test_take, name='admission_test_take'),
    path('admission-test/results/', views.admission_test_results, name='admission_test_results'),

    # Survey URLs
    path('survey/', views.student_survey, name='student_survey'),
    path('survey/results/', views.survey_results, name='survey_results'),

    # Student HTMX actions
    path('bookmark-course/<int:course_id>/', views.bookmark_course, name='bookmark_course'),
    path('delete-interest/<int:interest_id>/', views.delete_interest, name='delete_interest'),
    path('add-interest/', views.add_interest, name='add_interest'),
    path('add-suggested-interest/', views.add_suggested_interest, name='add_suggested_interest'),
    path('update-career-goal/<int:goal_id>/', views.update_career_goal, name='update_career_goal'),
    path('add-career-goal/', views.add_career_goal, name='add_career_goal'),
    path('delete-career-goal/<int:goal_id>/', views.delete_career_goal, name='delete_career_goal'),
    path('delete-academic-record/<int:record_id>/', views.delete_academic_record, name='delete_academic_record'),
    path('add-academic-record/', views.add_academic_record, name='add_academic_record'),


    # Management URLs
    path('management/dashboard/', views.admin_dashboard, name='management_dashboard'),
    path('management/students/', views.admin_students, name='management_students'),
    path('management/courses/', views.admin_courses, name='management_courses'),
    path('management/advising/', views.admin_advising, name='management_advising'),
    path('management/reports/', views.admin_reports, name='management_reports'),

    # Student CRUD URLs
    path('management/students/create/', views.admin_student_create, name='admin_student_create'),
    path('management/students/<int:student_id>/view/', views.admin_student_view, name='admin_student_view'),
    path('management/students/<int:student_id>/edit/', views.admin_student_edit, name='admin_student_edit'),
    path('management/students/<int:student_id>/delete/', views.admin_student_delete, name='admin_student_delete'),

    # Course CRUD URLs
    path('management/courses/create/', views.admin_course_create, name='admin_course_create'),
    path('management/courses/<int:course_id>/view/', views.admin_course_view, name='admin_course_view'),
    path('management/courses/<int:course_id>/edit/', views.admin_course_edit, name='admin_course_edit'),
    path('management/courses/<int:course_id>/delete/', views.admin_course_delete, name='admin_course_delete'),

    # Department CRUD URLs
    path('management/departments/', views.admin_departments, name='management_departments'),
    path('management/departments/create/', views.admin_department_create, name='management_department_create'),
    path('management/departments/<int:department_id>/edit/', views.admin_department_edit, name='management_department_edit'),
    path('management/departments/<int:department_id>/delete/', views.admin_department_delete, name='management_department_delete'),

    # Admission Test Management URLs
    path('management/admission-tests/', views.admin_admission_tests, name='management_admission_tests'),
    path('management/admission-tests/create/', views.admin_admission_test_create, name='admin_admission_test_create'),
    path('management/admission-tests/import/', views.admin_admission_test_import, name='admin_admission_test_import'),
    path('management/admission-tests/import/template/', views.admin_admission_test_template, name='admin_admission_test_template'),
    path('management/admission-tests/<int:test_id>/view/', views.admin_admission_test_view, name='admin_admission_test_view'),
    path('management/admission-tests/<int:test_id>/edit/', views.admin_admission_test_edit, name='admin_admission_test_edit'),
    path('management/admission-tests/<int:test_id>/delete/', views.admin_admission_test_delete, name='admin_admission_test_delete'),
    path('management/admission-tests/<int:test_id>/toggle-status/', views.admin_admission_test_toggle_status, name='admin_admission_test_toggle_status'),
    path('management/admission-tests/statistics/', views.admin_admission_test_statistics, name='admin_admission_test_statistics'),

    # HTMX API URLs
    path('api/recent-activity/', views.recent_activity_api, name='api_recent_activity'),
    path('api/student-list/', views.student_list_api, name='api_student_list'),
    path('api/search-students/', views.search_students_api, name='api_search_students'),
    path('api/course-list/', views.course_list_api, name='api_course_list'),
    path('api/search-courses/', views.search_courses_api, name='api_search_courses'),
]
