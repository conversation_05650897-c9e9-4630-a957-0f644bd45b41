{% extends 'base.html' %}

{% block title %}Manage Courses - {{ block.super }}{% endblock %}



{% block sidebar %}
    {% include 'admin/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<div class="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-white/20">
    <!-- Header and Add Course Button -->
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-semibold text-gray-800">Course Management</h2>
        <a href="{% url 'admin_course_create' %}" class="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition-transform transform hover:scale-105 inline-flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
            </svg>
            Add Course
        </a>
    </div>

    <!-- Search and Filter -->
    <div class="mb-6">
        <input type="text" placeholder="Search courses by name, code, or department..."
               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
               hx-post="{% url 'api_search_courses' %}"
               hx-trigger="keyup changed delay:500ms"
               hx-target="#course-list"
               hx-indicator=".htmx-indicator">
    </div>

    <!-- Course List -->
    <div id="course-list" hx-get="{% url 'api_course_list' %}" hx-trigger="load">
        <div class="htmx-indicator flex items-center justify-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            <span class="ml-2 text-gray-600">Loading course list...</span>
        </div>
    </div>
</div>
{% endblock %}
