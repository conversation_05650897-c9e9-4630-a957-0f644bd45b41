from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.test import Client
from course_matcher.models import StudentProfile, AcademicRecord, AdmissionTestAttempt, StudentSurvey, Course, Department
from course_matcher.views import get_profile_completion_status


class Command(BaseCommand):
    help = 'Test recommendation unlocking functionality'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Testing recommendation unlocking...'))
        
        # Create test client
        client = Client()
        
        # Create or get a test user
        user, created = User.objects.get_or_create(
            username='test_rec_student',
            defaults={
                'first_name': 'Test',
                'last_name': 'RecStudent',
                'email': '<EMAIL>'
            }
        )
        user.set_password('testpass123')
        user.save()
        
        # Create student profile
        student, created = StudentProfile.objects.get_or_create(
            user=user,
            defaults={
                'student_id': 'TESTREC001',
                'year': 'sophomore',
                'interests': [],
                'career_goals': '',
                'major': None,  # Explicitly set to None for testing
            }
        )
        
        self.stdout.write(f'Testing with student: {student}')
        
        # Login
        login_success = client.login(username='test_rec_student', password='testpass123')
        if not login_success:
            self.stdout.write(self.style.ERROR('Failed to login'))
            return
        
        # Test 1: Access recommendations with incomplete profile
        self.stdout.write('\n--- Test 1: Incomplete Profile ---')

        # Check current profile status
        status = get_profile_completion_status(student)
        self.stdout.write(f'Profile completion status: {status}')
        self.stdout.write(f'All completed: {all(status.values())}')

        response = client.get('/recommendations/')
        self.stdout.write(f'Response status: {response.status_code}')

        if response.status_code == 200:
            content = response.content.decode('utf-8')
            if response.context and 'profile_incomplete' in response.context:
                self.stdout.write('✅ Profile incomplete flag detected in context')
                self.stdout.write(f'Profile incomplete: {response.context["profile_incomplete"]}')
                if response.context.get('missing_components'):
                    self.stdout.write(f'Missing components: {response.context["missing_components"]}')

            if 'Complete Your Profile to Unlock Recommendations' in content:
                self.stdout.write('✅ Incomplete profile message shown in UI')
            else:
                self.stdout.write('❌ Incomplete profile message NOT shown in UI')
                # Debug: show what's actually in the content
                if len(content) > 500:
                    self.stdout.write(f'Content preview: {content[:500]}...')
        
        # Test 2: Complete profile step by step
        self.stdout.write('\n--- Test 2: Completing Profile Step by Step ---')

        # Add interests
        student.interests = ['Machine Learning', 'Web Development']
        student.save()
        response = client.get('/recommendations/')
        status = get_profile_completion_status(student)
        self.stdout.write(f'After adding interests: {sum(status.values())}/6 complete')

        # Add career goals
        student.career_goals = 'I want to become a software engineer specializing in AI.'
        student.save()
        response = client.get('/recommendations/')
        status = get_profile_completion_status(student)
        self.stdout.write(f'After adding career goals: {sum(status.values())}/6 complete')
        
        # Add academic record and more courses for recommendations
        dept, _ = Department.objects.get_or_create(
            code='CS',
            defaults={'name': 'Computer Science'}
        )

        # Create multiple courses for better recommendations
        courses_data = [
            ('CS101', 'Introduction to Programming', 'Basic programming concepts', 'beginner'),
            ('CS201', 'Data Structures', 'Advanced data structures and algorithms', 'intermediate'),
            ('CS301', 'Machine Learning', 'Introduction to machine learning concepts', 'advanced'),
            ('CS302', 'Web Development', 'Full-stack web development', 'intermediate'),
            ('CS401', 'Artificial Intelligence', 'AI principles and applications', 'advanced'),
        ]

        created_courses = []
        for code, name, desc, difficulty in courses_data:
            course, _ = Course.objects.get_or_create(
                code=code,
                defaults={
                    'name': name,
                    'description': desc,
                    'credits': 3,
                    'department': dept,
                    'difficulty': difficulty
                }
            )
            created_courses.append(course)

        # Use the first course for academic record
        course = created_courses[0]
        record, _ = AcademicRecord.objects.get_or_create(
            student=student,
            course=course,
            defaults={
                'semester': 'fall',
                'year': 2023,
                'grade': 'A'
            }
        )
        response = client.get('/recommendations/')
        status = get_profile_completion_status(student)
        self.stdout.write(f'After adding academic record: {sum(status.values())}/6 complete')

        # Add major
        student.major = dept
        student.save()
        response = client.get('/recommendations/')
        status = get_profile_completion_status(student)
        self.stdout.write(f'After adding major: {sum(status.values())}/6 complete')
        
        # Add admission test
        attempt, _ = AdmissionTestAttempt.objects.get_or_create(
            student=student,
            defaults={
                'is_completed': True,
                'total_score': 85,
                'max_possible_score': 100,
                'percentage_score': 85.0
            }
        )
        response = client.get('/recommendations/')
        status = get_profile_completion_status(student)
        self.stdout.write(f'After completing admission test: {sum(status.values())}/6 complete')
        
        # Add survey
        survey, _ = StudentSurvey.objects.get_or_create(
            student=student,
            defaults={
                'learning_style': 'visual',
                'study_preference': 'group',
                'time_preference': 'morning',
                'motivation_factors': ['career_advancement', 'personal_interest'],
                'stress_level': 5,
                'extracurricular_time': 10,
                'work_hours': 15,
                'technology_comfort': 8,
                'career_certainty': 7
            }
        )
        
        # Test 3: Access recommendations with complete profile - Top 3 Test
        self.stdout.write('\n--- Test 3: Complete Profile - Top 3 Recommendations ---')
        status = get_profile_completion_status(student)
        self.stdout.write(f'Final completion status: {sum(status.values())}/6 complete')

        # Generate recommendations for the test student
        from course_matcher.recommendation_service import RecommendationEngine
        engine = RecommendationEngine()
        recommendations = engine.get_recommendations(student)
        self.stdout.write(f'Generated {len(recommendations)} recommendations for test student')

        # Show the top 3 recommendations that will be displayed
        for i, rec in enumerate(recommendations[:3], 1):
            self.stdout.write(f'  Top #{i}: {rec.course.code} - {rec.course.name} ({rec.confidence_score:.1f}% confidence)')

        response = client.get('/recommendations/')
        self.stdout.write(f'Response status: {response.status_code}')

        if response.status_code == 200:
            content = response.content.decode('utf-8')

            # Check if profile_incomplete flag is False or not present
            if response.context:
                profile_incomplete = response.context.get('profile_incomplete', False)
                if not profile_incomplete:
                    self.stdout.write('✅ Profile complete - no incomplete flag')
                else:
                    self.stdout.write('❌ Profile still marked as incomplete')

                # Check for recommendation content
                if 'recommendations' in response.context:
                    recommendations = response.context.get('recommendations', [])
                    self.stdout.write(f'Recommendations in context: {len(recommendations)} items')

                    # Test top 3 functionality
                    if len(recommendations) <= 3:
                        self.stdout.write(f'✅ Showing {len(recommendations)} recommendations (≤ 3)')

                        # Check if recommendations are ordered by confidence score
                        if len(recommendations) > 1:
                            scores = [rec.confidence_score for rec in recommendations]
                            if scores == sorted(scores, reverse=True):
                                self.stdout.write('✅ Recommendations ordered by confidence score (highest first)')
                            else:
                                self.stdout.write('❌ Recommendations NOT properly ordered by confidence score')
                                self.stdout.write(f'Scores: {scores}')

                        # Show details of each recommendation
                        for i, rec in enumerate(recommendations, 1):
                            self.stdout.write(f'  #{i}: {rec.course.code} - {rec.course.name} ({rec.confidence_score:.1f}% confidence)')
                            if rec.reasoning:
                                self.stdout.write(f'      Reasoning: {rec.reasoning[:100]}...')
                    else:
                        self.stdout.write(f'❌ Too many recommendations shown: {len(recommendations)} (should be ≤ 3)')

                # Check for showing_top_3 flag
                showing_top_3 = response.context.get('showing_top_3', False)
                total_recommendations = response.context.get('total_recommendations', 0)
                if showing_top_3:
                    self.stdout.write(f'✅ Top 3 flag set, total available: {total_recommendations}')
                else:
                    self.stdout.write('⚠️ Top 3 flag not set')

            # Check if the new template content is shown
            if 'Your Top Course Recommendations' in content:
                self.stdout.write('✅ New top 3 recommendations template shown')
            elif 'No Recommendations Available' in content:
                self.stdout.write('⚠️ No recommendations available message shown')
            else:
                self.stdout.write('? Unknown template state')
                # Show a snippet of the content for debugging
                if len(content) > 1000:
                    self.stdout.write(f'Content snippet: {content[500:1000]}...')
            
        # Test 4: Dashboard view
        self.stdout.write('\n--- Test 4: Dashboard View ---')
        response = client.get('/dashboard/')
        if response.status_code == 200:
            if response.context:
                progress = response.context.get('progress', {})
                if progress.get('all_completed'):
                    self.stdout.write('✅ Dashboard shows profile as complete')
                else:
                    self.stdout.write('❌ Dashboard shows profile as incomplete')
                    self.stdout.write(f'Progress: {progress}')
            else:
                self.stdout.write('❌ No context available in dashboard response')
        
        # Cleanup
        self.stdout.write('\nCleaning up test data...')
        AcademicRecord.objects.filter(student=student).delete()
        AdmissionTestAttempt.objects.filter(student=student).delete()
        StudentSurvey.objects.filter(student=student).delete()
        # Clean up created courses (except CS101 which might be used elsewhere)
        Course.objects.filter(code__in=['CS201', 'CS301', 'CS302', 'CS401']).delete()
        student.delete()
        user.delete()
        
        self.stdout.write(self.style.SUCCESS('Recommendation test completed!'))
