from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from course_matcher.models import (
    Department, Course, StudentProfile, AcademicRecord, 
    AdmissionTest, AdmissionTestAttempt, StudentSurvey, Recommendation
)
from course_matcher.recommendation_service import RecommendationEngine
import random
from faker import Faker

fake = Faker()


class Command(BaseCommand):
    help = 'Generate comprehensive mock data for testing and demonstration'

    def add_arguments(self, parser):
        parser.add_argument(
            '--departments',
            type=int,
            default=8,
            help='Number of departments to create (default: 8)'
        )
        parser.add_argument(
            '--courses-per-dept',
            type=int,
            default=6,
            help='Number of courses per department (default: 6)'
        )
        parser.add_argument(
            '--students',
            type=int,
            default=25,
            help='Number of students to create (default: 25)'
        )
        parser.add_argument(
            '--admission-tests',
            type=int,
            default=50,
            help='Number of admission test questions (default: 50)'
        )
        parser.add_argument(
            '--clear-existing',
            action='store_true',
            help='Clear existing data before generating new data'
        )

    def handle(self, *args, **options):
        if options['clear_existing']:
            self.stdout.write('Clearing existing data...')
            self.clear_existing_data()

        self.stdout.write(self.style.SUCCESS('Starting mock data generation...'))
        
        # Generate departments
        departments = self.create_departments(options['departments'])
        self.stdout.write(f'Created {len(departments)} departments')
        
        # Generate courses
        courses = self.create_courses(departments, options['courses_per_dept'])
        self.stdout.write(f'Created {len(courses)} courses')
        
        # Generate admission test questions
        test_questions = self.create_admission_tests(options['admission_tests'])
        self.stdout.write(f'Created {len(test_questions)} admission test questions')
        
        # Generate students
        students = self.create_students(departments, options['students'])
        self.stdout.write(f'Created {len(students)} students')
        
        # Generate academic records
        records = self.create_academic_records(students, courses)
        self.stdout.write(f'Created {len(records)} academic records')
        
        # Generate admission test attempts
        attempts = self.create_admission_test_attempts(students)
        self.stdout.write(f'Created {len(attempts)} admission test attempts')
        
        # Generate surveys
        surveys = self.create_surveys(students)
        self.stdout.write(f'Created {len(surveys)} student surveys')
        
        # Generate recommendations
        recommendations = self.create_recommendations(students)
        self.stdout.write(f'Generated {len(recommendations)} recommendations')
        
        self.stdout.write(self.style.SUCCESS('Mock data generation completed successfully!'))
        self.print_summary(departments, courses, students)

    def clear_existing_data(self):
        """Clear existing data (except superuser accounts)"""
        Recommendation.objects.all().delete()
        StudentSurvey.objects.all().delete()
        AdmissionTestAttempt.objects.all().delete()
        AdmissionTest.objects.all().delete()
        AcademicRecord.objects.all().delete()
        StudentProfile.objects.all().delete()
        Course.objects.all().delete()
        Department.objects.all().delete()
        # Don't delete superuser accounts
        User.objects.filter(is_superuser=False).delete()

    def create_departments(self, count):
        """Create sample departments"""
        departments_data = [
            ('Computer Science', 'CS', 'Study of computational systems, algorithms, and software development'),
            ('Business Administration', 'BUS', 'Management, finance, marketing, and organizational leadership'),
            ('Engineering', 'ENG', 'Applied sciences and mathematics to design and build systems'),
            ('Psychology', 'PSY', 'Scientific study of mind, behavior, and mental processes'),
            ('Biology', 'BIO', 'Study of living organisms and their interactions with environment'),
            ('Mathematics', 'MATH', 'Study of numbers, structures, patterns, and logical reasoning'),
            ('English Literature', 'ENG-LIT', 'Study of written works, language, and literary analysis'),
            ('Physics', 'PHYS', 'Study of matter, energy, and their interactions in the universe'),
            ('Chemistry', 'CHEM', 'Study of matter, its properties, and chemical reactions'),
            ('History', 'HIST', 'Study of past events, societies, and human civilization'),
        ]
        
        departments = []
        for i in range(min(count, len(departments_data))):
            name, code, description = departments_data[i]
            dept, created = Department.objects.get_or_create(
                code=code,
                defaults={'name': name, 'description': description}
            )
            departments.append(dept)
        
        return departments

    def create_courses(self, departments, courses_per_dept):
        """Create sample courses for each department"""
        course_templates = {
            'CS': [
                ('Introduction to Programming', 'Basic programming concepts and problem-solving', 'beginner'),
                ('Data Structures & Algorithms', 'Fundamental data structures and algorithmic thinking', 'intermediate'),
                ('Database Systems', 'Database design, SQL, and data management', 'intermediate'),
                ('Web Development', 'Full-stack web application development', 'intermediate'),
                ('Machine Learning', 'AI and machine learning fundamentals', 'advanced'),
                ('Software Engineering', 'Software development methodologies and practices', 'advanced'),
            ],
            'BUS': [
                ('Business Fundamentals', 'Introduction to business concepts and practices', 'beginner'),
                ('Marketing Principles', 'Marketing strategies and consumer behavior', 'beginner'),
                ('Financial Management', 'Corporate finance and investment analysis', 'intermediate'),
                ('Operations Management', 'Business operations and supply chain', 'intermediate'),
                ('Strategic Management', 'Business strategy and competitive analysis', 'advanced'),
                ('Entrepreneurship', 'Starting and managing new ventures', 'advanced'),
            ],
            'ENG': [
                ('Engineering Mathematics', 'Mathematical foundations for engineering', 'beginner'),
                ('Physics for Engineers', 'Applied physics principles in engineering', 'beginner'),
                ('Materials Science', 'Properties and applications of engineering materials', 'intermediate'),
                ('Thermodynamics', 'Energy systems and heat transfer', 'intermediate'),
                ('Control Systems', 'Automatic control and system design', 'advanced'),
                ('Project Management', 'Engineering project planning and execution', 'advanced'),
            ],
        }
        
        courses = []
        for dept in departments:
            templates = course_templates.get(dept.code, [
                ('Introduction to ' + dept.name, f'Foundational concepts in {dept.name.lower()}', 'beginner'),
                ('Intermediate ' + dept.name, f'Intermediate topics in {dept.name.lower()}', 'intermediate'),
                ('Advanced ' + dept.name, f'Advanced studies in {dept.name.lower()}', 'advanced'),
                ('Research Methods', f'Research methodologies in {dept.name.lower()}', 'intermediate'),
                ('Capstone Project', f'Final project in {dept.name.lower()}', 'advanced'),
                ('Special Topics', f'Current topics in {dept.name.lower()}', 'advanced'),
            ])
            
            for i in range(min(courses_per_dept, len(templates))):
                name, description, difficulty = templates[i]
                course_code = f"{dept.code}{100 + i * 100 + random.randint(1, 99)}"
                
                course, created = Course.objects.get_or_create(
                    code=course_code,
                    defaults={
                        'name': name,
                        'description': description,
                        'credits': random.choice([3, 4]),
                        'department': dept,
                        'difficulty': difficulty,
                        'topics': self.generate_course_topics(dept.name, name)
                    }
                )
                courses.append(course)
        
        return courses

    def generate_course_topics(self, dept_name, course_name):
        """Generate relevant topics for a course"""
        topic_pools = {
            'Computer Science': ['programming', 'algorithms', 'data structures', 'software engineering', 'databases', 'web development', 'machine learning', 'artificial intelligence'],
            'Business': ['management', 'finance', 'marketing', 'strategy', 'leadership', 'operations', 'entrepreneurship', 'economics'],
            'Engineering': ['mathematics', 'physics', 'design', 'systems', 'materials', 'thermodynamics', 'mechanics', 'electronics'],
        }
        
        # Default topics if department not found
        default_topics = ['theory', 'practice', 'analysis', 'research', 'application', 'methodology']
        
        # Find matching topic pool
        topics = default_topics
        for key, pool in topic_pools.items():
            if key.lower() in dept_name.lower():
                topics = pool
                break
        
        # Return 3-5 random topics
        return random.sample(topics, min(random.randint(3, 5), len(topics)))

    def create_admission_tests(self, count):
        """Create admission test questions"""
        subjects = ['math', 'english', 'science', 'history', 'general_knowledge']
        difficulties = ['easy', 'medium', 'hard']
        question_types = ['multiple_choice', 'true_false']

        questions = []
        for i in range(count):
            subject = random.choice(subjects)
            difficulty = random.choice(difficulties)
            question_type = random.choice(question_types)

            question_text = f"Sample {subject} question #{i+1} ({difficulty} level)"

            if question_type == 'multiple_choice':
                options = [
                    f"Option A for question {i+1}",
                    f"Option B for question {i+1}",
                    f"Option C for question {i+1}",
                    f"Option D for question {i+1}",
                ]
                correct_answer = random.choice(['A', 'B', 'C', 'D'])
            else:  # true_false
                options = ['True', 'False']
                correct_answer = random.choice(['True', 'False'])

            test = AdmissionTest.objects.create(
                question_text=question_text,
                question_type=question_type,
                subject_area=subject,
                difficulty_level=difficulty,
                options=options,
                correct_answer=correct_answer,
                explanation=f"This is the explanation for question {i+1}",
                points=random.randint(1, 3),
                is_active=True
            )
            questions.append(test)

        return questions

    def create_students(self, departments, count):
        """Create sample students with varying completion levels"""
        students = []
        years = ['freshman', 'sophomore', 'junior', 'senior']

        for i in range(count):
            # Create user
            username = f"student_{i+1:03d}"
            user = User.objects.create_user(
                username=username,
                email=f"{username}@university.edu",
                password='password123',
                first_name=fake.first_name(),
                last_name=fake.last_name()
            )

            # Create student profile with varying completion levels
            completion_level = random.choice(['incomplete', 'partial', 'complete'])

            student = StudentProfile.objects.create(
                user=user,
                student_id=f"STU{2024}{i+1:04d}",
                year=random.choice(years),
                phone_number=fake.phone_number()[:15],
                address=fake.address()[:200],
                date_of_birth=fake.date_of_birth(minimum_age=18, maximum_age=25),
                expected_graduation_year=random.randint(2024, 2028),
                interests=self.generate_student_interests(completion_level),
                career_goals=self.generate_career_goals(completion_level),
                major=random.choice(departments) if completion_level in ['partial', 'complete'] else None
            )
            students.append(student)

        return students

    def generate_student_interests(self, completion_level):
        """Generate student interests based on completion level"""
        if completion_level == 'incomplete':
            return []

        interest_pool = [
            'Machine Learning', 'Web Development', 'Data Science', 'Mobile Apps',
            'Cybersecurity', 'Game Development', 'Artificial Intelligence',
            'Business Strategy', 'Marketing', 'Finance', 'Entrepreneurship',
            'Research', 'Teaching', 'Consulting', 'Project Management'
        ]

        count = 2 if completion_level == 'partial' else random.randint(3, 6)
        return random.sample(interest_pool, min(count, len(interest_pool)))

    def generate_career_goals(self, completion_level):
        """Generate career goals based on completion level"""
        if completion_level == 'incomplete':
            return ""

        goals = [
            "I want to become a software engineer at a tech company.",
            "My goal is to start my own business in the technology sector.",
            "I aspire to work in data science and machine learning.",
            "I want to pursue a career in cybersecurity and information protection.",
            "My goal is to become a product manager at a leading company.",
            "I want to work in research and development in my field.",
            "I aspire to become a consultant helping businesses with technology.",
            "My goal is to work in the finance industry using my analytical skills.",
        ]

        return random.choice(goals) if completion_level in ['partial', 'complete'] else ""

    def create_academic_records(self, students, courses):
        """Create academic records for students"""
        records = []
        grades = ['A', 'A-', 'B+', 'B', 'B-', 'C+', 'C']
        semesters = ['fall', 'spring', 'summer']
        years = [2022, 2023, 2024]

        for student in students:
            # Each student takes 3-8 courses
            num_courses = random.randint(3, 8)
            student_courses = random.sample(courses, min(num_courses, len(courses)))

            for course in student_courses:
                record = AcademicRecord.objects.create(
                    student=student,
                    course=course,
                    semester=random.choice(semesters),
                    year=random.choice(years),
                    grade=random.choice(grades)
                )
                records.append(record)

        return records

    def create_admission_test_attempts(self, students):
        """Create admission test attempts for students"""
        attempts = []

        for student in students:
            # 70% of students complete the admission test
            if random.random() < 0.7:
                score = random.randint(60, 100)
                attempt = AdmissionTestAttempt.objects.create(
                    student=student,
                    is_completed=True,
                    total_score=score,
                    max_possible_score=100,
                    percentage_score=float(score)
                )
                attempts.append(attempt)

        return attempts

    def create_surveys(self, students):
        """Create student surveys"""
        surveys = []
        learning_styles = ['visual', 'auditory', 'kinesthetic', 'reading']
        study_preferences = ['individual', 'group', 'mixed']
        time_preferences = ['morning', 'afternoon', 'evening', 'night']
        motivation_factors = [
            ['career_advancement'], ['personal_interest'], ['family_expectations'],
            ['career_advancement', 'personal_interest'], ['personal_interest', 'skill_development']
        ]

        for student in students:
            # 60% of students complete the survey
            if random.random() < 0.6:
                survey = StudentSurvey.objects.create(
                    student=student,
                    learning_style=random.choice(learning_styles),
                    study_preference=random.choice(study_preferences),
                    time_preference=random.choice(time_preferences),
                    motivation_factors=random.choice(motivation_factors),
                    stress_level=random.randint(1, 10),
                    extracurricular_time=random.randint(0, 20),
                    work_hours=random.randint(0, 30),
                    technology_comfort=random.randint(1, 10),
                    career_certainty=random.randint(1, 10)
                )
                surveys.append(survey)

        return surveys

    def create_recommendations(self, students):
        """Generate recommendations for students with complete profiles"""
        recommendations = []
        engine = RecommendationEngine()

        for student in students:
            try:
                # Check if student has complete profile
                from course_matcher.views import get_profile_completion_status
                status = get_profile_completion_status(student)

                if all(status.values()):
                    student_recommendations = engine.get_recommendations(student)
                    recommendations.extend(student_recommendations)
            except Exception as e:
                self.stdout.write(f"Error generating recommendations for {student}: {e}")

        return recommendations

    def print_summary(self, departments, courses, students):
        """Print a summary of generated data"""
        self.stdout.write('\n' + '='*50)
        self.stdout.write(self.style.SUCCESS('MOCK DATA GENERATION SUMMARY'))
        self.stdout.write('='*50)

        self.stdout.write(f"📚 Departments: {len(departments)}")
        for dept in departments:
            self.stdout.write(f"   • {dept.name} ({dept.code})")

        self.stdout.write(f"\n📖 Courses: {len(courses)}")
        self.stdout.write(f"👥 Students: {len(students)}")
        self.stdout.write(f"📝 Academic Records: {AcademicRecord.objects.count()}")
        self.stdout.write(f"🧪 Admission Test Questions: {AdmissionTest.objects.count()}")
        self.stdout.write(f"✅ Test Attempts: {AdmissionTestAttempt.objects.count()}")
        self.stdout.write(f"📊 Surveys: {StudentSurvey.objects.count()}")
        self.stdout.write(f"🎯 Recommendations: {Recommendation.objects.count()}")

        # Profile completion stats
        complete_profiles = 0
        for student in students:
            from course_matcher.views import get_profile_completion_status
            status = get_profile_completion_status(student)
            if all(status.values()):
                complete_profiles += 1

        self.stdout.write(f"\n✨ Complete Profiles: {complete_profiles}/{len(students)}")
        self.stdout.write('='*50)
        self.stdout.write('🚀 Ready for testing and demonstration!')
