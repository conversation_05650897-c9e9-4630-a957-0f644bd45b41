{% extends 'base.html' %}

{% block title %}Import Admission Test Questions{% endblock %}

{% block sidebar %}
    {% include 'admin/partials/sidebar.html' %}
{% endblock %}

{% block extra_css %}
<style>
    .file-upload-area {
        border: 2px dashed #d1d5db;
        border-radius: 0.75rem;
        padding: 3rem;
        text-align: center;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.5);
        backdrop-filter: blur(10px);
    }
    
    .file-upload-area:hover {
        border-color: #3b82f6;
        background: rgba(59, 130, 246, 0.05);
    }
    
    .file-upload-area.dragover {
        border-color: #3b82f6;
        background: rgba(59, 130, 246, 0.1);
        transform: scale(1.02);
    }
    
    .upload-icon {
        width: 4rem;
        height: 4rem;
        margin: 0 auto 1rem;
        color: #6b7280;
    }
    
    .requirements-list {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        border-radius: 0.75rem;
        padding: 1.5rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .requirement-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 0.75rem;
    }
    
    .requirement-icon {
        width: 1.25rem;
        height: 1.25rem;
        color: #10b981;
        margin-right: 0.75rem;
        margin-top: 0.125rem;
        flex-shrink: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-white/20">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h2 class="text-2xl font-semibold text-gray-800">Bulk Import Admission Test Questions</h2>
            <p class="text-gray-600 mt-1">Import up to {{ max_questions }} questions at once from a CSV file</p>
        </div>
        <div class="flex space-x-3">
            <a href="{% url 'admin_admission_test_template' %}" 
               class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition-transform transform hover:scale-105 inline-flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Download Template
            </a>
            <a href="{% url 'management_admission_tests' %}" 
               class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition-transform transform hover:scale-105 inline-flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Tests
            </a>
        </div>
    </div>

    <!-- Import Form -->
    <form method="post" enctype="multipart/form-data" id="importForm">
        {% csrf_token %}
        
        <!-- File Upload Area -->
        <div class="mb-8">
            <label class="block text-sm font-medium text-gray-700 mb-3">
                Select CSV File
            </label>
            <div class="file-upload-area" id="fileUploadArea">
                <div class="upload-icon">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Drop your CSV file here</h3>
                <p class="text-gray-600 mb-4">or click to browse and select a file</p>
                <input type="file" name="csv_file" id="csvFile" accept=".csv" class="hidden" required>
                <button type="button" onclick="document.getElementById('csvFile').click()" 
                        class="bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                    Choose File
                </button>
            </div>
            <div id="selectedFile" class="mt-3 hidden">
                <div class="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
                    <svg class="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span id="fileName" class="text-green-800 font-medium"></span>
                    <button type="button" onclick="clearFile()" class="ml-auto text-green-600 hover:text-green-800">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-center">
            <button type="submit" name="preview" 
                    class="bg-primary-600 hover:bg-primary-700 text-white font-bold py-3 px-8 rounded-lg shadow-md transition-transform transform hover:scale-105 inline-flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                Preview Questions
            </button>
        </div>
    </form>
</div>

<!-- Requirements Section -->
<div class="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- File Format Requirements -->
    <div class="requirements-list">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">File Format Requirements</h3>
        <div class="space-y-3">
            <div class="requirement-item">
                <svg class="requirement-icon" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-sm text-gray-700">CSV file format only</span>
            </div>
            <div class="requirement-item">
                <svg class="requirement-icon" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-sm text-gray-700">Maximum {{ max_questions }} questions per import</span>
            </div>
            <div class="requirement-item">
                <svg class="requirement-icon" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-sm text-gray-700">Required columns: question_text, subject_area, difficulty_level, answer_options, correct_answer</span>
            </div>
            <div class="requirement-item">
                <svg class="requirement-icon" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-sm text-gray-700">Answer options must be in JSON array format: ["option1", "option2"]</span>
            </div>
        </div>
    </div>

    <!-- Valid Values -->
    <div class="requirements-list">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Valid Values</h3>
        <div class="space-y-4">
            <div>
                <h4 class="text-sm font-medium text-gray-700 mb-2">Subject Areas:</h4>
                <div class="flex flex-wrap gap-2">
                    {% for choice in subject_choices %}
                    <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">{{ choice.0 }}</span>
                    {% endfor %}
                </div>
            </div>
            <div>
                <h4 class="text-sm font-medium text-gray-700 mb-2">Difficulty Levels:</h4>
                <div class="flex flex-wrap gap-2">
                    {% for choice in difficulty_choices %}
                    <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">{{ choice.0 }}</span>
                    {% endfor %}
                </div>
            </div>
            <div>
                <h4 class="text-sm font-medium text-gray-700 mb-2">Points Range:</h4>
                <span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">1 - 10 points</span>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileUploadArea = document.getElementById('fileUploadArea');
    const csvFile = document.getElementById('csvFile');
    const selectedFile = document.getElementById('selectedFile');
    const fileName = document.getElementById('fileName');

    // File input change handler
    csvFile.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            const file = e.target.files[0];
            fileName.textContent = file.name;
            selectedFile.classList.remove('hidden');
        }
    });

    // Drag and drop handlers
    fileUploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        fileUploadArea.classList.add('dragover');
    });

    fileUploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        fileUploadArea.classList.remove('dragover');
    });

    fileUploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        fileUploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            csvFile.files = files;
            fileName.textContent = files[0].name;
            selectedFile.classList.remove('hidden');
        }
    });

    // Click to upload
    fileUploadArea.addEventListener('click', function() {
        csvFile.click();
    });
});

function clearFile() {
    document.getElementById('csvFile').value = '';
    document.getElementById('selectedFile').classList.add('hidden');
}
</script>
{% endblock %}
